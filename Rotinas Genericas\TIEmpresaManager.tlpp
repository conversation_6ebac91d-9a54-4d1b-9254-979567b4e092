#Include "totvs.ch"
#Include "tryexception.ch"

namespace ti

/*/{Protheus.doc} TIEmpresaManager
Classe para gerenciamento seguro de conexões com diferentes empresas
Evita alteração direta de cEmpAnt e utiliza RpcSetEnv de forma controlada
@type class
@version P12
<AUTHOR>
@since 01/08/2025
/*/
Class TIEmpresaManager

    // Propriedades privadas
    Private cEmpresaOriginal    As Character
    Private cFilialOriginal     As Character
    Private aEmpresas          As Array
    Private lConectado         As Logical
    Private cEmpresaAtual      As Character
    Private cFilialAtual       As Character
    Private oLogger            As Object

    // Métodos públicos
    Method New() Constructor
    Method ConnectToEmpresa(cIdentificador, cTipoId, cFilial)
    Method GetEmpresaInfo(cIdentificador, cTipoId)
    Method GetFilialInfo(cEmpresa, cFilial)
    Method GetEmpresaByCNPJ(cCNPJ)
    Method GetEmpresaByCode(cCodEmp)
    Method GetAllEmpresas()
    Method RestoreOriginalConnection()
    Method IsConnected()
    Method GetCurrentEmpresa()
    Method GetCurrentFilial()
    Method ExecuteInEmpresa(cIdentificador, cTipoId, cFilial, bCodeBlock)
    Method Destroy()

    // Métodos privados
    Private Method LoadEmpresas()
    Private Method ValidateConnection()
    Private Method FindEmpresa(cIdentificador, cTipoId)
    Private Method LogOperation(cOperation, cDetails)

EndClass

/*/{Protheus.doc} TIEmpresaManager::New
Construtor da classe
@type method
@return object, Instância da classe
/*/
Method New() Class TIEmpresaManager
    
    // Armazena contexto original
    ::cEmpresaOriginal := cEmpAnt
    ::cFilialOriginal  := cFilAnt
    ::lConectado       := .F.
    ::cEmpresaAtual    := ""
    ::cFilialAtual     := ""
    ::aEmpresas        := {}
    
    // Carrega informações das empresas
    ::LoadEmpresas()
    
    // Log da inicialização
    ::LogOperation("INIT", "Classe inicializada - Empresa Original: " + ::cEmpresaOriginal + "/" + ::cFilialOriginal)
    
Return Self

/*/{Protheus.doc} TIEmpresaManager::LoadEmpresas
Carrega informações de todas as empresas disponíveis
@type method
@return logical, Sucesso na operação
/*/
Method LoadEmpresas() Class TIEmpresaManager
    
    TryException
        ::aEmpresas := FwLoadSM0()
        ::LogOperation("LOAD", "Carregadas " + cValToChar(Len(::aEmpresas)) + " empresas")
        Return .T.
    CatchException Using oError
        ::LogOperation("ERROR", "Erro ao carregar empresas: " + oError:Description)
        Return .F.
    EndException
    
Return .F.

/*/{Protheus.doc} TIEmpresaManager::ConnectToEmpresa
Conecta a uma empresa específica usando RpcSetEnv
@type method
@param cIdentificador, character, Identificador da empresa (CNPJ, código, etc.)
@param cTipoId, character, Tipo do identificador: "CNPJ", "CODIGO", "NOME"
@param cFilial, character, Código da filial (opcional)
@return logical, Sucesso na conexão
/*/
Method ConnectToEmpresa(cIdentificador, cTipoId, cFilial) Class TIEmpresaManager
    
    Local aEmpInfo := {}
    Local lSuccess := .F.
    
    Default cTipoId := "CNPJ"
    Default cFilial := ""
    
    // Busca informações da empresa
    aEmpInfo := ::FindEmpresa(cIdentificador, cTipoId)
    
    If Len(aEmpInfo) == 0
        ::LogOperation("ERROR", "Empresa não encontrada: " + cIdentificador + " (Tipo: " + cTipoId + ")")
        Return .F.
    EndIf
    
    // Define filial se não informada
    If Empty(cFilial)
        cFilial := aEmpInfo[2] // Filial padrão da empresa
    EndIf
    
    TryException
        // Executa RpcSetEnv para trocar empresa
        RpcClearEnv()
        RpcSetType(3)
        RpcSetEnv(aEmpInfo[1], cFilial)
        
        // Atualiza controle interno
        ::cEmpresaAtual := aEmpInfo[1]
        ::cFilialAtual  := cFilial
        ::lConectado    := .T.
        
        ::LogOperation("CONNECT", "Conectado: " + aEmpInfo[1] + "/" + cFilial + " (" + AllTrim(aEmpInfo[6]) + ")")
        lSuccess := .T.
        
    CatchException Using oError
        ::LogOperation("ERROR", "Erro na conexão: " + oError:Description)
        lSuccess := .F.
    EndException
    
Return lSuccess

/*/{Protheus.doc} TIEmpresaManager::GetEmpresaInfo
Retorna informações de uma empresa sem conectar
@type method
@param cIdentificador, character, Identificador da empresa
@param cTipoId, character, Tipo do identificador
@return array, Informações da empresa
/*/
Method GetEmpresaInfo(cIdentificador, cTipoId) Class TIEmpresaManager
    
    Local aEmpInfo := {}
    Local aResult  := {}
    
    Default cTipoId := "CNPJ"
    
    aEmpInfo := ::FindEmpresa(cIdentificador, cTipoId)
    
    If Len(aEmpInfo) > 0
        aResult := {;
            {"CODIGO",    aEmpInfo[1]},;
            {"FILIAL",    aEmpInfo[2]},;
            {"NOME_EMP",  AllTrim(aEmpInfo[6])},;
            {"NOME_FIL",  AllTrim(aEmpInfo[7])},;
            {"CNPJ",      AllTrim(aEmpInfo[18])},;
            {"RAZAO",     AllTrim(aEmpInfo[6]) + " - " + AllTrim(aEmpInfo[7])};
        }
    EndIf
    
Return aResult

/*/{Protheus.doc} TIEmpresaManager::GetEmpresaByCNPJ
Busca empresa por CNPJ (8 ou 14 dígitos)
@type method
@param cCNPJ, character, CNPJ da empresa
@return array, Informações da empresa
/*/
Method GetEmpresaByCNPJ(cCNPJ) Class TIEmpresaManager
    
    Local nPos := 0
    Local cCNPJClean := AllTrim(cCNPJ)
    
    If Len(cCNPJClean) == 8
        nPos := AScan(::aEmpresas, {|x| Left(AllTrim(x[18]), 8) == cCNPJClean})
    ElseIf Len(cCNPJClean) == 14
        nPos := AScan(::aEmpresas, {|x| AllTrim(x[18]) == cCNPJClean})
    EndIf
    
    If nPos > 0
        Return ::aEmpresas[nPos]
    EndIf
    
Return {}

/*/{Protheus.doc} TIEmpresaManager::GetEmpresaByCode
Busca empresa por código
@type method
@param cCodEmp, character, Código da empresa
@return array, Informações da empresa
/*/
Method GetEmpresaByCode(cCodEmp) Class TIEmpresaManager
    
    Local nPos := AScan(::aEmpresas, {|x| AllTrim(x[1]) == AllTrim(cCodEmp)})
    
    If nPos > 0
        Return ::aEmpresas[nPos]
    EndIf
    
Return {}

/*/{Protheus.doc} TIEmpresaManager::ExecuteInEmpresa
Executa um bloco de código em uma empresa específica e retorna ao contexto original
@type method
@param cIdentificador, character, Identificador da empresa
@param cTipoId, character, Tipo do identificador
@param cFilial, character, Código da filial
@param bCodeBlock, codeblock, Bloco de código a ser executado
@return variant, Resultado da execução do bloco
/*/
Method ExecuteInEmpresa(cIdentificador, cTipoId, cFilial, bCodeBlock) Class TIEmpresaManager
    
    Local uResult := Nil
    Local lConnected := .F.
    
    Default cTipoId := "CNPJ"
    Default cFilial := ""
    
    // Conecta à empresa
    lConnected := ::ConnectToEmpresa(cIdentificador, cTipoId, cFilial)
    
    If lConnected
        TryException
            // Executa o bloco de código
            uResult := Eval(bCodeBlock)
            ::LogOperation("EXECUTE", "Bloco executado com sucesso")
        CatchException Using oError
            ::LogOperation("ERROR", "Erro na execução do bloco: " + oError:Description)
            uResult := Nil
        EndException
        
        // Restaura conexão original
        ::RestoreOriginalConnection()
    EndIf
    
Return uResult

/*/{Protheus.doc} TIEmpresaManager::RestoreOriginalConnection
Restaura a conexão original
@type method
@return logical, Sucesso na operação
/*/
Method RestoreOriginalConnection() Class TIEmpresaManager
    
    Local lSuccess := .F.
    
    TryException
        RpcClearEnv()
        RpcSetType(3)
        RpcSetEnv(::cEmpresaOriginal, ::cFilialOriginal)
        
        ::lConectado := .F.
        ::cEmpresaAtual := ""
        ::cFilialAtual := ""
        
        ::LogOperation("RESTORE", "Conexão original restaurada: " + ::cEmpresaOriginal + "/" + ::cFilialOriginal)
        lSuccess := .T.
        
    CatchException Using oError
        ::LogOperation("ERROR", "Erro ao restaurar conexão: " + oError:Description)
        lSuccess := .F.
    EndException
    
Return lSuccess

/*/{Protheus.doc} TIEmpresaManager::GetAllEmpresas
Retorna todas as empresas disponíveis
@type method
@return array, Array com todas as empresas
/*/
Method GetAllEmpresas() Class TIEmpresaManager
Return AClone(::aEmpresas)

/*/{Protheus.doc} TIEmpresaManager::IsConnected
Verifica se está conectado a uma empresa diferente da original
@type method
@return logical, Status da conexão
/*/
Method IsConnected() Class TIEmpresaManager
Return ::lConectado

/*/{Protheus.doc} TIEmpresaManager::GetCurrentEmpresa
Retorna código da empresa atual
@type method
@return character, Código da empresa atual
/*/
Method GetCurrentEmpresa() Class TIEmpresaManager
Return ::cEmpresaAtual

/*/{Protheus.doc} TIEmpresaManager::GetCurrentFilial
Retorna código da filial atual
@type method
@return character, Código da filial atual
/*/
Method GetCurrentFilial() Class TIEmpresaManager
Return ::cFilialAtual

/*/{Protheus.doc} TIEmpresaManager::GetFilialInfo
Retorna informações de uma filial específica
@type method
@param cEmpresa, character, Código da empresa
@param cFilial, character, Código da filial
@return array, Informações da filial
/*/
Method GetFilialInfo(cEmpresa, cFilial) Class TIEmpresaManager

    Local nPos := AScan(::aEmpresas, {|x| AllTrim(x[1]) == AllTrim(cEmpresa) .And. AllTrim(x[2]) == AllTrim(cFilial)})
    Local aResult := {}

    If nPos > 0
        aResult := {;
            {"CODIGO",    ::aEmpresas[nPos][1]},;
            {"FILIAL",    ::aEmpresas[nPos][2]},;
            {"NOME_EMP",  AllTrim(::aEmpresas[nPos][6])},;
            {"NOME_FIL",  AllTrim(::aEmpresas[nPos][7])},;
            {"CNPJ",      AllTrim(::aEmpresas[nPos][18])},;
            {"ENDERECO",  AllTrim(::aEmpresas[nPos][8])},;
            {"CIDADE",    AllTrim(::aEmpresas[nPos][9])},;
            {"UF",        AllTrim(::aEmpresas[nPos][10])},;
            {"CEP",       AllTrim(::aEmpresas[nPos][11])};
        }
    EndIf

Return aResult

/*/{Protheus.doc} TIEmpresaManager::Destroy
Destrutor da classe - restaura conexão original
@type method
@return logical, Sucesso na operação
/*/
Method Destroy() Class TIEmpresaManager

    ::LogOperation("DESTROY", "Destruindo instância da classe")

    If ::lConectado
        ::RestoreOriginalConnection()
    EndIf

    ::aEmpresas := {}

Return .T.

/*/{Protheus.doc} TIEmpresaManager::FindEmpresa
Busca empresa por diferentes critérios
@type method
@param cIdentificador, character, Identificador da empresa
@param cTipoId, character, Tipo do identificador
@return array, Informações da empresa encontrada
/*/
Method FindEmpresa(cIdentificador, cTipoId) Class TIEmpresaManager

    Local nPos := 0
    Local cId := AllTrim(Upper(cIdentificador))

    Do Case
        Case Upper(cTipoId) == "CNPJ"
            If Len(cId) == 8
                nPos := AScan(::aEmpresas, {|x| Left(AllTrim(x[18]), 8) == cId})
            ElseIf Len(cId) == 14
                nPos := AScan(::aEmpresas, {|x| AllTrim(x[18]) == cId})
            EndIf

        Case Upper(cTipoId) == "CODIGO"
            nPos := AScan(::aEmpresas, {|x| AllTrim(x[1]) == cId})

        Case Upper(cTipoId) == "NOME"
            nPos := AScan(::aEmpresas, {|x| Upper(AllTrim(x[6])) == cId})

        Otherwise
            // Tenta buscar por CNPJ como padrão
            If Len(cId) == 8
                nPos := AScan(::aEmpresas, {|x| Left(AllTrim(x[18]), 8) == cId})
            ElseIf Len(cId) == 14
                nPos := AScan(::aEmpresas, {|x| AllTrim(x[18]) == cId})
            Else
                nPos := AScan(::aEmpresas, {|x| AllTrim(x[1]) == cId})
            EndIf
    EndCase

    If nPos > 0
        Return ::aEmpresas[nPos]
    EndIf

Return {}

/*/{Protheus.doc} TIEmpresaManager::ValidateConnection
Valida se a conexão atual está funcionando
@type method
@return logical, Status da validação
/*/
Method ValidateConnection() Class TIEmpresaManager

    Local lValid := .F.

    TryException
        // Testa acesso a uma tabela básica
        DbSelectArea("SM0")
        lValid := .T.
    CatchException Using oError
        ::LogOperation("ERROR", "Erro na validação da conexão: " + oError:Description)
        lValid := .F.
    EndException

Return lValid

/*/{Protheus.doc} TIEmpresaManager::LogOperation
Log das operações da classe
@type method
@param cOperation, character, Tipo da operação
@param cDetails, character, Detalhes da operação
/*/
Method LogOperation(cOperation, cDetails) Class TIEmpresaManager

    Local cLogMsg := "[TIEmpresaManager] " + cOperation + ": " + cDetails

    // Log usando FwLogMsg se disponível
    If FindFunction("FwLogMsg")
        FwLogMsg("INFO", , "TIEMPMANAGER", FunName(), "", "01", cLogMsg)
    EndIf

    // Também pode usar ConOut para debug
    ConOut(cLogMsg)

Return
