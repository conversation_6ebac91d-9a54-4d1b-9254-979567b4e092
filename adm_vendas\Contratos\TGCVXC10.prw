#include "totvs.ch"
#INCLUDE "FWMVCDEF.CH"
//TIADMVIN-3138
User Function Tgcvxc10
Return

	CLASS TGCVXC10

		Data cContrato
		Data cRevisa
		Data cCmpIni
		Data cCmpFim
		Data cAMFim
		Data cCmpGvIni
		Data cAMGvIni
		Data aEstruPH5
		Data aEstruPH6
		Data aEstruPH7
		Data aEstruCTR
		Data aPH5
		Data aPH6
		Data aPH7
		Data aCTR
		Data aAMAtu
		Data oModelo
		Data cMsgErro
		Data cRevAtuDb
		Data aCroItem
		Data aMensal

		Data cPlanilha
		Data cItem
		Data cProduto
		Data cDescPro
		Data cTipRec
		Data cTipCnt
		Data dVencto
		Data cFreFat
		Data cTpPago
		Data nDiafat

		Data nCarenVnd
		Data cCarIniVnd
		Data cCarFinVnd
		Data cCondPG
		Data cCodISS
		Data cNotaSe
		Data cMoeda
		Data cMascCC
		Data cStatRM
		Data cGrpEmp
		Data cUniNeg
		Data cPropos
		Data cRevPro
		Data cItmPro
		Data cFoldPr
		Data dDtIni
		Data dConIni
		Data dConFin
		Data cAnoMesI
		Data cAnoMesF
		Data nQtdMes
		Data cAMAtu
		Data nQuant
		Data nQtdRev
		Data nVlrUniCtr
		Data nPeriodico
		Data nImpostC
		Data cModImpC
		Data cCmpFIni
		Data cLinRec
		Data lAudita
		Data cSituac
		Data lBilling
		Data lRoyalties
		Data lCorpora


		METHOD New(cContrato, cRevisa, cCmpIni, cCmpFim)
		METHOD Cria()
		METHOD Destroy()
		METHOD Estrura(cAlias)
		METHOD EstruCTR()
		METHOD LoadDB()
		METHOD LoadModel(oModelo)
		METHOD Totaliza()

		METHOD NewPH5()
		METHOD PosPH5(cCampo)
		METHOD GetPH5(cCampo, nLinha)
		METHOD PutPH5(cCampo, uConteudo, nLinha)

		METHOD NewPH6()
		METHOD PosPH6(cCampo)
		METHOD GetPH6(cCampo, nLinha)
		METHOD PutPH6(cCampo, uConteudo, nLinha, lSoma)
		METHOD FindPH6(cChave)

		METHOD NewPH7()
		METHOD PosPH7(cCampo)
		METHOD GetPH7(cCampo, nLinha)
		METHOD PutPH7(cCampo, uConteudo, nLinha, lSoma)
		METHOD FindPH7(cChave)

		METHOD NewCTR()
		METHOD PosCTR(cCampo)
		METHOD GetCTR(cCampo, nLinha)
		METHOD PutCTR(cCampo, uConteudo, nLinha, lSoma)

		METHOD AtuaPH6(nPosPH5)
		METHOD StatPH6(nPosPH6)

		METHOD AtuaPH7(nPosPH5, nPosPH6)
		METHOD StatCTRL(nPosPH6)
		METHOD StatPH7()

		METHOD Calc()
		METHOD CalcItem(oModCNB)

		METHOD LoadCXL(aReajuste, aAjustFin)
		METHOD LoadDbCXL(aReajuste, aAjustFin)
		METHOD LoadP68(aP68Intera)
		METHOD LoadDbP68(aP68Intera)
		Method LoadCli(aCliente, aExcecao, aHistoric, aPHG, aCNCMain)
		METHOD LoadPH3(aCancela)
		METHOD LoadDbPH3(aCancela)
		METHOD LoadPH4(aCarenVnd, aCarencia, aBonifica)
		METHOD LoadDbPH4(aCarenVnd, aCarencia, aBonifica)
		METHOD LoadPH8(aTransf)
		METHOD LoadDbPH8(aTransf)
		METHOD LoadPH5(aFinanceiro)
		METHOD LoadPNG(aCliente, aRoyalties)
		METHOD LoadPHN(aTroca)
		METHOD LoadDbPHN(aTroca)
		METHOD IsRoyalties()
		METHOD LoadPHD(aCtrlPer)
		METHOD LoadPHM(aBilling)
		METHOD LoadPQS(aReajExtra)
		METHOD LoadCroFin( aCarenVnd)
		METHOD CalcCroFin(aCliente, nPosCli, aSituac, aCancela, aCarencia, aBonifica, aReajuste, aAjustFin, aP68Intera, aTransf, aFinanceiro, aCtrlPer, aBilling, aRoyalties, aTroca, aReajExtra, aExcecao, aHistoric, aPHG, aCNCMain)
		METHOD RetQtdOri()
		METHOD Refresh()
		METHOD DelPH5Old()
		Method IndexPH5()
		METHOD DelModOld()
		METHOD ModRefresh()
		METHOD CheckMensal(oModCNB)
		METHOD ProcMensal()


	ENDCLASS

//-----------------------------------------------------------------
METHOD New(cContrato, cRevisa, cCmpIni, cCmpFim) CLASS TGCVXC10
	::cContrato:= cContrato
	::cRevisa  := cRevisa
	::aEstruPH5:= {}
	::aEstruPH6:= {}
	::aEstruPH7:= {}
	::aEstruCTR:= {}
	::aPH5     := {}
	::aPH6     := {}
	::aPH7     := {}
	::aCTR     := {}
	::aAMAtu   := {}
	::cMsgErro := ""
	::cRevAtuDb:= ""
	::aCroItem := {}
	::aMensal  := {}
	::cCmpIni  := ""
	::cCmpFim  := ""
	::cAMFim   := ""
	If cCmpIni == NIL
		::cCmpGvIni:= AMtoCmp(Left(Dtos(date()), 6))
	Else
		::cCmpGvIni:= cCmpIni
	EndIf
	If cCmpFim == NIL
		::cCmpFim  := AMtoCmp(Left(Dtos(date()), 6))
	Else
		::cCmpFim  := cCmpFim
	EndIf
	::cAMFim   := CmpToAM(::cCmpFim)
	::cAMGvIni := CmpToAM(::cCmpGvIni)


	::cPlanilha  := ""
	::cItem      := ""
	::cProduto   := ""
	::cDescPro   := ""
	::cTipRec    := ""
	::cTipCnt    := ""
	::dVencto    := Ctod("")
	::cFreFat    := ""
	::cTpPago    := ""
	::nDiafat    := 0
	::nCarenVnd  := 0
	::cCarIniVnd := ""
	::cCarFinVnd := ""
	::cCondPG    := ""
	::cCodISS    := ""
	::cNotaSe    := ""
	::cMoeda     := ""
	::cMascCC    := ""
	::cStatRM    := ""
	::cGrpEmp    := ""
	::cUniNeg    := ""
	::cPropos    := ""
	::cRevPro    := ""
	::cItmPro    := ""
	::cFoldPr    := ""
	::dDtIni     := Ctod("")
	::dConIni    := Ctod("")
	::dConFin    := Ctod("")
	::cAnoMesI   := ""
	::cAnoMesF   := ""
	::nQtdMes    := 0
	::cAMAtu     := ""
	::nQuant     := 0
	::nQtdRev    := 0
	::nVlrUniCtr := 0
	::nPeriodico := 0
	::nImpostC   := 0
	::cModImpC   := ""
	::cCmpFIni   := ""
	::cLinRec    := ""
	::lAudita    := .F.
	::cSituac    := ""
	::lBilling   := .F.
	::lRoyalties := .F.
	::lCorpora   := .F.

Return

METHOD Destroy() CLASS TGCVXC10
	Local ni := 0

	For ni:=1 to Len(::aCroItem)
		::aCroItem[ni, 5]:FreeChild()
		FreeObj(::aCroItem[ni, 5])
		::aCroItem[ni, 5] := Nil
	Next

	aSize(::aCroItem , 0)
	aSize(::aEstruPH5, 0)
	aSize(::aEstruPH6, 0)
	aSize(::aEstruPH7, 0)
	aSize(::aEstruCTR, 0)
	aSize(::aPH5     , 0)
	aSize(::aPH6     , 0)
	aSize(::aPH7     , 0)
	aSize(::aCTR     , 0)
	aSize(::aAMAtu   , 0)

	::aCroItem  := Nil
	::aEstruPH5 := Nil
	::aEstruPH6 := Nil
	::aEstruPH7 := Nil
	::aEstruCTR := Nil
	::aPH5      := Nil
	::aPH6      := Nil
	::aPH7      := Nil
	::aCTR      := Nil
	::aAMAtu    := Nil

Return


Method Cria() CLASS TGCVXC10
	::aEstruPH5 := Self:Estrura("PH5")
	::aEstruPH6 := Self:Estrura("PH6")
	::aEstruPH7 := Self:Estrura("PH7")
	::aEstruCTR := Self:EstruCTR()
Return

METHOD Estrura(cAlias) CLASS TGCVXC10
	Local aArea    := GetArea()
	Local aFields  := FWSX3Util():GetAllFields( cAlias, .F. )
	Local cCampo   := ""
	Local aEstru   := {}
	Local ni       := 0

	For ni := 1 to Len(aFields)
		cCampo := aFields[ni]
		If Ascan(aEstru, {|x| x[2] == cCampo }) > 0
			Loop
		EndIF
		If Alltrim( cCampo ) $ cAlias + "_FILIAL|"
			Loop
		EndIf
		aadd(aEstru, {  FWX3Titulo( cCampo )               , ;
			FwGetSx3Cache(cCampo, "X3_CAMPO"  ), ;
			FwGetSx3Cache(cCampo, "X3_PICTURE"), ;
			FwGetSx3Cache(cCampo, "X3_TAMANHO"), ;
			FwGetSx3Cache(cCampo, "X3_DECIMAL"), ;
			FwGetSx3Cache(cCampo, "X3_VALID"  ), ;
			FwGetSx3Cache(cCampo, "X3_USADO"  ), ;
			FwGetSx3Cache(cCampo, "X3_TIPO"   ), ;
			FwGetSx3Cache(cCampo, "X3_F3"     ), ;
			FwGetSx3Cache(cCampo, "X3_CONTEXT"), ;
			FwGetSx3Cache(cCampo, "X3_CBOX"   ), ;
			FwGetSx3Cache(cCampo, "X3_RELACAO"), ;
			FwGetSx3Cache(cCampo, "X3_WHEN"   )  })
	End

	RestArea(aArea)
Return aClone(aEstru)


Method EstruCTR() CLASS TGCVXC10
	Local aEstru := {}

	aadd(aEstru, {"nTotal" , "nTotal"  , , 15, 2, Nil, Nil, "N"})
	aadd(aEstru, {"nFat"   , "nFat"    , , 15, 2, Nil, Nil, "N"})
	aadd(aEstru, {"nFatur" , "nFatur"  , , 15, 2, Nil, Nil, "N"})
	aadd(aEstru, {"nRefat" , "nRefat"  , , 15, 2, Nil, Nil, "N"})
	aadd(aEstru, {"nNotFat", "nNotFat" , , 15, 2, Nil, Nil, "N"})
	aadd(aEstru, {"nNfCanc", "nNfCanc" , , 15, 2, Nil, Nil, "N"})
	aadd(aEstru, {"nAberto", "nAberto" , , 15, 2, Nil, Nil, "N"})
	aadd(aEstru, {"nStat00", "nStat00" , , 15, 2, Nil, Nil, "N"})
	aadd(aEstru, {"nStat01", "nStat01" , , 15, 2, Nil, Nil, "N"})
	aadd(aEstru, {"nStat02", "nStat02" , , 15, 2, Nil, Nil, "N"})
	aadd(aEstru, {"nStat03", "nStat03" , , 15, 2, Nil, Nil, "N"})
	aadd(aEstru, {"nStat04", "nStat04" , , 15, 2, Nil, Nil, "N"})
	aadd(aEstru, {"nStat05", "nStat05" , , 15, 2, Nil, Nil, "N"})
	aadd(aEstru, {"nStat06", "nStat06" , , 15, 2, Nil, Nil, "N"})
	aadd(aEstru, {"nStat07", "nStat07" , , 15, 2, Nil, Nil, "N"})
	aadd(aEstru, {"nStat08", "nStat08" , , 15, 2, Nil, Nil, "N"})

Return aClone(aEstru)

Method LoadDB() CLASS TGCVXC10
	Local aArea    := GetArea()
	Local aAreaPH5 := PH5->(GetArea())
	Local cChave   := ""
	Local aReg     := {}
	Local nx       := 0
	Local cCampo   := ""

	PH5->(DbSetOrder(12)) //PH5_FILIAL+PH5_CONTRA+PH5_REVISA+PH5_ANOMES+PH5_NUMERO+PH5_CONDIC+PH5_CLIENT+PH5_LOJA+PH5_CONDPG+PH5_NOTASE+PH5_MOEDA+PH5_MASCCC+PH5_GU+PH5_SEQ

	cChave := xFilial("PH5") + ::cContrato + ::cRevisa
	PH5->(DbSeek(cChave))
	While PH5->(!Eof() .And. PH5_FILIAL + PH5_CONTRA + PH5_REVISA == cChave)
		If ! IsBlind()
			IncProc("Carregando cronograma: " +  PH5->PH5_COMPET)
			ProcessMessage()
			If lAbortPrint
				RestArea(aAreaPH5)
				RestArea(aArea)
				Return
			EndIf
		EndIf
		aReg := {}
		For nx:= 1 to len(::aEstruPH5)
			cCampo := ::aEstruPH5[nx, 2]
			aadd(aReg, PH5->(FieldGet(FieldPos(cCampo))))
		Next
		aadd(aReg, .F.)
		aadd(::aPH5, aClone(aReg))

		Self:AtuaPH6(len(::aPH5))
		PH5->(DbSkip())
	End
	Self:StatPH7()

	RestArea(aAreaPH5)
	RestArea(aArea)
Return

Method LoadModel(oModelo) CLASS TGCVXC10
	Local aReg     := {}
	Local nx       := 0
	Local nPH7     := 0
	Local nPH6     := 0
	Local nPH5     := 0
	Local cCampo   := ""
	Local oModPH7  := oModelo:GetModel("PH7DETAIL")
	Local oModPH6  := oModelo:GetModel("PH6DETAIL")
	Local oModPH5  := oModelo:GetModel("PH5DETAIL")

	::oModelo := oModelo

	If oModPH7 == NIL
		Self:LoadDB()
		Return
	EndIf

	For nPH7 := 1 to oModPH7:Length()
		oModPH7:GoLine(nPH7)
		If oModPH7:IsDeleted()
			Loop
		EndIf

		If CmpToAm(oModPH7:GetValue("PH7_COMPET")) < ::cAMGvIni
			Loop
		EndIf

		If ! IsBlind()
			IncProc("Carregando cronograma -  Planilha: " + oModPH7:GetValue("PH7_NUMERO") + " " +  oModPH7:GetValue("PH7_COMPET"))
			ProcessMessage()
			If lAbortPrint
				Return
			EndIf
		EndIf

		For nPH6 := 1 to oModPH6:Length()
			oModPH6:GoLine(nPH6)
			If oModPH6:IsDeleted()
				Loop
			EndIf
			For nPH5 := 1 to oModPH5:Length()
				oModPH5:GoLine(nPH5)
				If oModPH5:IsDeleted()
					Loop
				EndIf
				// Atualizar saldo de nota cancelado PH5_SLDCAN
				AtuSldCan(oModPH5)

				aReg := {}
				For nx:= 1 to len(::aEstruPH5)
					cCampo := ::aEstruPH5[nx, 2]
					aadd(aReg, oModPH5:GetValue(cCampo))
				Next
				aadd(aReg, .F.)
				aadd(::aPH5, aClone(aReg))

				Self:AtuaPH6(len(::aPH5))
			Next
		Next
	Next
	Self:StatPH7()

Return

Static Function AtuSldCan(oModPH5)
	Local cChave   := ""
	Local aArea    := GetArea()
	Local aAreaPHP := PHP->(GetArea())
	Local aAreaPH5 := PH5->(GetArea())
	Local nSldCan  := 0
	Local cQuery   := ""
	Local cTMP     := GetNextAlias()
	Local cUniNeg  := oModPH5:GetValue("PH5_UNINEG")
	Local cNota    := oModPH5:GetValue("PH5_NOTA")
	Local cSerie   := oModPH5:GetValue("PH5_SERIE")
	Local cItemNF  := oModPH5:GetValue("PH5_ITEMNF")

	If Empty(cNota)
		Return
	EndIf

	cQuery := ""
	cQuery += " SELECT PHP.PHP_VLRTOT  "
	cQuery += "   FROM  PHP000 PHP "
	cQuery += "  WHERE PHP.PHP_FILIAL = '" + cUniNeg + "' "
	cQuery += "    AND PHP.PHP_NFORI  = '" + cNota   + "' "
	cQuery += "    AND PHP.PHP_SERORI = '" + cSerie  + "' "
	cQuery += "    AND PHP.PHP_ITORI  = '" + cItemNF + "' "
	cQuery += "    AND PHP.PHP_TIPO   = '2' "
	cQuery += "    AND PHP.PHP_FLAG   <> 'E' "
	cQuery += "    AND PHP.D_E_L_E_T_ = ' ' "

	DbUseArea(.T., "TOPCONN", TcGenQry(NIL, NIL, cQuery), cTMP, .T., .F.)
	If (cTMP)->(Eof())
		(cTMP)->(DbCloseArea())
		Return
	EndIf

	While ! (cTMP)->(EOF())
		nSldCan  += (cTMP)->PHP_VLRTOT
		(cTMP)->(DbSkip())
	End
	(cTMP)->(DbCloseArea())

	CNTA300BlMd(oModPH5, .F.)
	oModPH5:LoadValue("PH5_SLDCAN", nSldCan)

	PH5->(DbSetOrder(1))
	cChave := oModPH5:GetValue("PH5_FILIAL")
	cChave += oModPH5:GetValue("PH5_CONTRA")
	cChave += oModPH5:GetValue("PH5_REVISA")
	cChave += oModPH5:GetValue("PH5_NUMERO")
	cChave += oModPH5:GetValue("PH5_COMPET")
	cChave += oModPH5:GetValue("PH5_CONDIC")
	cChave += oModPH5:GetValue("PH5_CLIENT")
	cChave += oModPH5:GetValue("PH5_LOJA"  )
	cChave += oModPH5:GetValue("PH5_CONDPG")
	cChave += oModPH5:GetValue("PH5_NOTASE")
	cChave += oModPH5:GetValue("PH5_MOEDA" )
	cChave += oModPH5:GetValue("PH5_MASCCC")
	cChave += oModPH5:GetValue("PH5_GU"    )
	cChave += oModPH5:GetValue("PH5_SEQ"   )
	cChave += oModPH5:GetValue("PH5_ITEM"  )

	PH5->(DbSeek(cChave))
	PH5->(RecLock("PH5", .F.))
	PH5->PH5_SLDCAN := nSldCan
	PH5->(MsUnLock())

	RestArea(aAreaPH5)
	RestArea(aAreaPHP)
	RestArea(aArea)

Return



Method Totaliza() CLASS TGCVXC10
	Local nx:= 0
	::aPH6 := {}
	::aPH7 := {}
	::aCTR := {}

	For nx:= 1 to len(::aPH5)
		Self:AtuaPH6(nx)
	Next
	Self:StatPH7()

Return


Method NewPH5() CLASS TGCVXC10
	Local aReg := {}
	Local nx

	For nx:= 1 to len(::aEstruPH5)
		cTipo := ::aEstruPH5[nx, 8]
		If cTipo == "C"
			aadd(aReg, " ")
		ElseIf cTipo == "N"
			aadd(aReg, 0)
		ElseIf cTipo == "D"
			aadd(aReg, ctod(" "))
		Else
			aadd(aReg, "")
		EndIf
	Next
	aadd(aReg, .F.)

	aadd(::aPH5, aClone(aReg) )

Return


Method PosPH5(cCampo) CLASS TGCVXC10
	Local np := 0

	np := Ascan(::aEstruPH5, {|x| Alltrim(x[2]) == Alltrim(cCampo)})

Return np

Method GetPH5(cCampo, nLinha) CLASS TGCVXC10
	Local cRet := ""
	Local np   := 0

	np:= Self:PosPH5(cCampo)
	If ! Empty(np)
		cRet := ::aPH5[nLinha, np]
	EndIf

Return cRet

Method PutPH5(cCampo, uConteudo, nLinha) CLASS TGCVXC10
	Local np   := 0

	nP:= Self:PosPH5(cCampo)
	If Empty(np)
		Return
	EndIf
	::aPH5[nLinha, np] := uConteudo

Return


Method NewPH6() CLASS TGCVXC10
	Local aReg := {}
	Local nx

	For nx:= 1 to len(::aEstruPH6)
		cTipo := ::aEstruPH6[nx, 8]
		If cTipo == "C"
			aadd(aReg, " ")
		ElseIf cTipo == "N"
			aadd(aReg, 0)
		ElseIf cTipo == "D"
			aadd(aReg, ctod(" "))
		Else
			aadd(aReg, "")
		EndIf
	Next
	aadd(aReg, .F.)

	aadd(::aPH6, aClone(aReg) )

Return

Method PosPH6(cCampo) CLASS TGCVXC10
	Local np := 0

	np := Ascan(::aEstruPH6, {|x| Alltrim(x[2]) == Alltrim(cCampo)})

Return np

Method GetPH6(cCampo, nLinha) CLASS TGCVXC10
	Local cRet := ""
	Local np   := 0

	nP:= Self:PosPH6(cCampo)
	If ! Empty(np)
		cRet := ::aPH6[nLinha, nP]
	EndIf

Return cRet

Method PutPH6(cCampo, uConteudo, nLinha, lSoma) CLASS TGCVXC10
	Local np   := 0
	Default lSoma := .F.

	nP:= Self:PosPH6(cCampo)
	If Empty(np)
		Return
	EndIf
	If lSoma
		::aPH6[nLinha, np] += uConteudo
	Else
		::aPH6[nLinha, np] := uConteudo
	EndIf

Return

Method FindPH6(cChave) CLASS TGCVXC10
	Local nP1  := Self:PosPH6("PH6_CONTRA" )
	Local nP2  := Self:PosPH6("PH6_REVISA" )
	Local nP3  := Self:PosPH6("PH6_NUMERO" )
	Local nP4  := Self:PosPH6("PH6_COMPET" )
	Local nP5  := Self:PosPH6("PH6_CONDIC" )
	Local nP6  := Self:PosPH6("PH6_CLIENT" )
	Local nP7  := Self:PosPH6("PH6_LOJA"   )
	Local nP8  := Self:PosPH6("PH6_CONDPG" )
	Local nP9  := Self:PosPH6("PH6_NOTASE" )
	Local nP10 := Self:PosPH6("PH6_MOEDA"  )
	Local nP11 := Self:PosPH6("PH6_MASCCC" )
	Local nP12 := Self:PosPH6("PH6_GU"     )
	Local nP13 := Self:PosPH6("PH6_SEQ"    )
	Local nP   := 0

	nP := Ascan(::aPH6, {|x| x[nP1]  + ;
		x[nP2]  + ;
		x[nP3]  + ;
		x[nP4]  + ;
		x[nP5]  + ;
		x[nP6]  + ;
		x[nP7]  + ;
		x[nP8]  + ;
		x[nP9]  + ;
		x[nP10] + ;
		x[nP11] + ;
		x[nP12] + ;
		x[nP13] ==  cChave })

Return nP

Method NewPH7() CLASS TGCVXC10
	Local aReg := {}
	Local nx
	Local cTipo := ""


	For nx:= 1 to len(::aEstruPH7)
		cTipo := ::aEstruPH7[nx, 8]
		If cTipo == "C"
			aadd(aReg, " ")
		ElseIf cTipo == "N"
			aadd(aReg, 0)
		ElseIf cTipo == "D"
			aadd(aReg, ctod(" "))
		Else
			aadd(aReg, "")
		EndIf
	Next
	aadd(aReg, .F.)
	aadd(::aPH7, aClone(aReg) )

Return

Method PosPH7(cCampo) CLASS TGCVXC10
	Local np := 0

	np := Ascan(::aEstruPH7, {|x| Alltrim(x[2]) == Alltrim(cCampo)})

Return np

Method GetPH7(cCampo, nLinha) CLASS TGCVXC10
	Local cRet := ""
	Local np   := 0

	nP:= Self:PosPH7(cCampo)
	If ! Empty(np)
		cRet := ::aPH7[nLinha, nP]
	EndIf

Return cRet

Method PutPH7(cCampo, uConteudo, nLinha, lSoma) CLASS TGCVXC10
	Local np   := 0
	Default lSoma := .F.

	nP:= Self:PosPH7(cCampo)
	If Empty(np)
		Return
	EndIf
	If lSoma
		::aPH7[nLinha, np] += uConteudo
	Else
		::aPH7[nLinha, np] := uConteudo
	EndIf

Return

Method FindPH7(cChave) CLASS TGCVXC10
	Local nP1 := Self:PosPH7("PH7_CONTRA" )
	Local nP2 := Self:PosPH7("PH7_REVISA" )
	Local nP3 := Self:PosPH7("PH7_NUMERO" )
	Local nP4 := Self:PosPH7("PH7_COMPET" )
	Local nP5 := Self:PosPH7("PH7_CONDIC" )
	Local nP6 := Self:PosPH7("PH7_MOEDA"  )
	Local nP7 := Self:PosPH7("PH7_GU"     )
	Local nP  := 0

	nP := Ascan(::aPH7, {|x| x[nP1]  + ;
		x[nP2]  + ;
		x[nP3]  + ;
		x[nP4]  + ;
		x[nP5]  + ;
		x[nP6]  + ;
		x[nP7]  ==  cChave })
Return nP

Method NewCTR() CLASS TGCVXC10
	Local aReg := {}
	Local nx
	Local cTipo := ""

	For nx:= 1 to len(::aEstruCTR)
		cTipo := ::aEstruCTR[nx, 8]
		If cTipo == "C"
			aadd(aReg, " ")
		ElseIf cTipo == "N"
			aadd(aReg, 0)
		ElseIf cTipo == "D"
			aadd(aReg, ctod(" "))
		Else
			aadd(aReg, "")
		EndIf
	Next
	aadd(aReg, 0)
	aadd(::aCTR, aClone(aReg) )

Return

Method PosCTR(cCampo) CLASS TGCVXC10
	Local np := 0

	np := Ascan(::aEstruCTR, {|x| Alltrim(x[2]) == Alltrim(cCampo)})

Return np

Method GetCTR(cCampo, nLinha) CLASS TGCVXC10
	Local cRet := ""
	Local np   := 0

	nP:= Self:PosCTR(cCampo)
	If ! Empty(np)
		cRet := ::aCTR[nLinha, nP]
	EndIf

Return cRet

Method PutCTR(cCampo, uConteudo, nLinha, lSoma) CLASS TGCVXC10
	Local np   := 0

	nP:= Self:PosCTR(cCampo)
	If Empty(np)
		Return
	EndIf
	If lSoma
		::aCTR[nLinha, np] += uConteudo
	Else
		::aCTR[nLinha, np] := uConteudo
	EndIf

Return


Method AtuaPH6(nPosPH5) CLASS TGCVXC10
	Local cChave := ""
	Local cChaveAux := ""
	Local nP
	Local aAreaF2   := SF2->(GetArea())

	If Self:aPH5[nPosPH5, len(Self:aPH5[nPosPH5])]
		Return
	EndIf

	cChave := Self:GetPH5("PH5_CONTRA", nPosPH5)
	cChave += Self:GetPH5("PH5_REVISA", nPosPH5)
	cChave += Self:GetPH5("PH5_NUMERO", nPosPH5)
	cChave += Self:GetPH5("PH5_COMPET", nPosPH5)
	cChave += Self:GetPH5("PH5_CONDIC", nPosPH5)
	cChave += Self:GetPH5("PH5_CLIENT", nPosPH5)
	cChave += Self:GetPH5("PH5_LOJA"  , nPosPH5)
	cChave += Self:GetPH5("PH5_CONDPG", nPosPH5)
	cChave += Self:GetPH5("PH5_NOTASE", nPosPH5)
	cChave += Self:GetPH5("PH5_MOEDA" , nPosPH5)
	cChave += Self:GetPH5("PH5_MASCCC", nPosPH5)
	cChave += Self:GetPH5("PH5_GU"    , nPosPH5)
	cChave += Self:GetPH5("PH5_SEQ"   , nPosPH5)

	nP := Self:FindPH6(cChave)

	If Empty(np)
		Self:NewPH6() // inicialiaza array
		np:= len(::aPH6)

		// Atualiza Chave
		Self:PutPH6("PH6_CONTRA", Self:GetPH5("PH5_CONTRA", nPosPH5), nP)
		Self:PutPH6("PH6_REVISA", Self:GetPH5("PH5_REVISA", nPosPH5), nP)
		Self:PutPH6("PH6_NUMERO", Self:GetPH5("PH5_NUMERO", nPosPH5), nP)
		Self:PutPH6("PH6_COMPET", Self:GetPH5("PH5_COMPET", nPosPH5), nP)
		Self:PutPH6("PH6_CONDIC", Self:GetPH5("PH5_CONDIC", nPosPH5), nP)
		Self:PutPH6("PH6_CLIENT", Self:GetPH5("PH5_CLIENT", nPosPH5), nP)
		Self:PutPH6("PH6_LOJA"  , Self:GetPH5("PH5_LOJA"  , nPosPH5), nP)
		Self:PutPH6("PH6_CONDPG", Self:GetPH5("PH5_CONDPG", nPosPH5), nP)
		Self:PutPH6("PH6_NOTASE", Self:GetPH5("PH5_NOTASE", nPosPH5), nP)
		Self:PutPH6("PH6_MOEDA" , Self:GetPH5("PH5_MOEDA" , nPosPH5), nP)
		Self:PutPH6("PH6_MASCCC", Self:GetPH5("PH5_MASCCC", nPosPH5), nP)
		Self:PutPH6("PH6_GU"    , Self:GetPH5("PH5_GU"    , nPosPH5), nP)
		Self:PutPH6("PH6_SEQ"   , Self:GetPH5("PH5_SEQ"   , nPosPH5), nP)

		Self:PutPH6("PH6_CODISS", Self:GetPH5("PH5_CODISS", nPosPH5), nP)
		Self:PutPH6("PH6_PEDVEN", Self:GetPH5("PH5_PEDVEN", nPosPH5), nP)
		Self:PutPH6("PH6_NOTA"  , Self:GetPH5("PH5_NOTA"  , nPosPH5), nP)
		Self:PutPH6("PH6_SERIE" , Self:GetPH5("PH5_SERIE" , nPosPH5), nP)
		Self:PutPH6("PH6_GRUPO" , Self:GetPH5("PH5_GRUPO" , nPosPH5), nP)
		Self:PutPH6("PH6_UNINEG", Self:GetPH5("PH5_UNINEG", nPosPH5), nP)
		Self:PutPH6("PH6_NUMMED", Self:GetPH5("PH5_NUMMED", nPosPH5), nP)
		Self:PutPH6("PH6_ANOMES", Self:GetPH5("PH5_ANOMES", nPosPH5), nP)

		cChaveAux := Self:GetPH5("PH5_UNINEG", nPosPH5) + Self:GetPH5("PH5_PEDVEN", nPosPH5)
		SC5->(DbSetOrder(1))
		If SC5->(DbSeek(cChaveAux))
			Self:PutPH6("PH6_DTGPED", SC5->C5_EMISSAO, nP)
		EndIf

		cChaveAux := Self:GetPH5("PH5_UNINEG", nPosPH5) + Self:GetPH5("PH5_NOTA", nPosPH5) + Self:GetPH5("PH5_SERIE", nPosPH5)
		SF2->(DbSetOrder(1))
		If SF2->(DbSeek(cChaveAux))
			Self:PutPH6("PH6_HRGNFS", SF2->F2_HORA    , nP)
			Self:PutPH6("PH6_DTGNFS", SF2->F2_EMISSAO , nP)
			Self:PutPH6("PH6_CMPFAT", StrZero(Month(SC5->C5_EMISSAO), 2) + "/" + StrZero(Year(SF2->F2_EMISSAO), 4) , nP)
		EndIf


		Self:PutPH6("PH6_DTMOED", Self:GetPH5("PH5_DTMOED", nPosPH5), nP)
		//Self:PutPH6("PH6_DTGMED", Self:GetPH5("PH5_DTGMED", nPosPH5), nP)
	EndIF

	Self:PutPH6("PH6_DTOPER", Self:GetPH5("PH5_DTOPER" , nPosPH5) , nP)
	Self:PutPH6("PH6_HROPER", Self:GetPH5("PH5_HROPER" , nPosPH5) , nP)

	Self:PutPH6("PH6_VLTOT" , Self:GetPH5("PH5_VLTOT" , nPosPH5), nP, .T.)
	Self:PutPH6("PH6_VLCANC", Self:GetPH5("PH5_VLCANC", nPosPH5), nP, .T.)
	Self:PutPH6("PH6_VLBONI", Self:GetPH5("PH5_VLBONI", nPosPH5), nP, .T.)
	Self:PutPH6("PH6_VLCARE", Self:GetPH5("PH5_VLCARE", nPosPH5), nP, .T.)
	Self:PutPH6("PH6_VLNOVA", Self:GetPH5("PH5_VLNOVA", nPosPH5), nP, .T.)
	Self:PutPH6("PH6_VLREAJ", Self:GetPH5("PH5_VLREAJ", nPosPH5), nP, .T.)
	Self:PutPH6("PH6_VLTRAN", Self:GetPH5("PH5_VLTRAN", nPosPH5), nP, .T.)
	Self:PutPH6("PH6_VLRFAT", Self:GetPH5("PH5_VLRFAT", nPosPH5), nP, .T.)
	Self:PutPH6("PH6_BUP"   , Self:GetPH5("PH5_BUP"   , nPosPH5), nP, .T.)
	Self:PutPH6("PH6_BDOWN" , Self:GetPH5("PH5_BDOWN" , nPosPH5), nP, .T.)
	Self:PutPH6("PH6_VLTRIB", Self:GetPH5("PH5_VLTRIB", nPosPH5), nP, .T.)
	Self:PutPH6("PH6_VLFATS", Self:GetPH5("PH5_VLFATS", nPosPH5), nP, .T.)
	Self:PutPH6("PH6_TXMOED", Self:GetPH5("PH5_TXMOED", nPosPH5), nP, .T.)
	Self:PutPH6("PH6_VLMOED", Self:GetPH5("PH5_VLMOED", nPosPH5), nP, .T.)
	Self:PutPH6("PH6_VLFAT2", Self:GetPH5("PH5_VLFAT2", nPosPH5), nP, .T.)
	Self:PutPH6("PH6_VLRAUD", Self:GetPH5("PH5_VLRAUD", nPosPH5), nP, .T.)
	Self:PutPH6("PH6_VLRTRC", Self:GetPH5("PH5_VLRTRC", nPosPH5), nP, .T.)
	Self:PutPH6("PH6_VLINCR", Self:GetPH5("PH5_VLINCR", nPosPH5), nP, .T.)
	Self:PutPH6("PH6_VLTRAE", Self:GetPH5("PH5_VLTRAE", nPosPH5), nP, .T.)
	Self:PutPH6("PH6_SLDCAN", Self:GetPH5("PH5_SLDCAN", nPosPH5), nP, .T.)
	Self:PutPH6("PH6_VARROY", Self:GetPH5("PH5_VARROY", nPosPH5), nP, .T.)
	Self:PutPH6("PH6_QTDREA", Self:GetPH5("PH5_QTDREA", nPosPH5), nP, .T.)
	Self:PutPH6("PH6_VLMULT", Self:GetPH5("PH5_VLMULT", nPosPH5), nP, .T.)
	Self:PutPH6("PH6_VLREA" , Self:GetPH5("PH5_VLREA" , nPosPH5), nP, .T.)
	Self:PutPH6("PH6_VLRBIL", Self:GetPH5("PH5_VLRBIL", nPosPH5), nP, .T.)
	Self:PutPH6("PH6_VARBIL", Self:GetPH5("PH5_VARBIL", nPosPH5), nP, .T.)
	Self:PutPH6("PH6_DLTTRC", Self:GetPH5("PH5_DLTTRC", nPosPH5), nP, .T.)
	Self:PutPH6("PH6_XVLRRE", Self:GetPH5("PH5_XVLRRE", nPosPH5), nP, .T.)
	Self:PutPH6("PH6_VLINTE", Self:GetPH5("PH5_VLINTE", nPosPH5), nP, .T.)
	Self:PutPH6("PH6_INTCAN", Self:GetPH5("PH5_INTCAN", nPosPH5), nP, .T.)
	Self:PutPH6("PH6_DIAFAT", Self:GetPH5("PH5_DIAFAT", nPosPH5), nP, .F.)


	Self:StatPH6(nP)

	Self:AtuaPH7(nPosPH5, np)

	RestArea(aAreaF2)
Return
/*
PH6_STATUS
00	Em aberto
01	Medição Gerada
02	Pedido de Venda
03	Represado
04	Refaturado
05	Faturado
06	Sem valor a faturar
07	NF Cancelada
08	NF Cancelada Parcial
*/

Method StatPH6(nPosPH6) CLASS TGCVXC10
	Local cSitPH6	:= ""
	Local nPH6ValDev := Self:GetPH6("PH6_SLDCAN", nPosPH6)
	Local nPH6Vlrfat := Self:GetPH6("PH6_VLRFAT", nPosPH6)
	Local cPH6Gu     := Self:GetPH6("PH6_GU"    , nPosPH6)
	Local cPH6Nota   := Self:GetPH6("PH6_NOTA"  , nPosPH6)
	Local cPH6Serie  := Self:GetPH6("PH6_SERIE" , nPosPH6)
	Local cPH6Pedven := Self:GetPH6("PH6_PEDVEN", nPosPH6)
	Local cPH6Nummed := Self:GetPH6("PH6_NUMMED", nPosPH6)

	If nPH6ValDev > 0
		If nPH6ValDev >= nPH6Vlrfat
			cSitPH6	:= "07"
		Else
			cSitPH6	:= "08"
			Self:PutPH6("PH6_CHVPAI", cPH6Gu + cPH6Nota + cPH6Serie , nPosPH6)
		EndIf
	ElseIf !Empty(cPH6Nota)
		cSitPH6	:= "05"
	ElseIf !Empty(cPH6Pedven)
		cSitPH6	:= "02"
	ElseIf !Empty(cPH6Nummed)
		cSitPH6	:= "01"
	Else
		If nPH6Vlrfat == 0
			cSitPH6	:= "06"
		Else
			cSitPH6	:= "00"
		EndIf
	EndIf
	Self:PutPH6("PH6_STATUS", cSitPH6, nPosPH6)

Return cSitPH6



Method AtuaPH7(nPosPH5, nPosPH6) CLASS TGCVXC10
	Local cChave := ""
	Local nP

	cChave := Self:GetPH5("PH5_CONTRA", nPosPH5)
	cChave += Self:GetPH5("PH5_REVISA", nPosPH5)
	cChave += Self:GetPH5("PH5_NUMERO", nPosPH5)
	cChave += Self:GetPH5("PH5_COMPET", nPosPH5)
	cChave += Self:GetPH5("PH5_CONDIC", nPosPH5)
	cChave += Self:GetPH5("PH5_MOEDA" , nPosPH5)
	cChave += Self:GetPH5("PH5_GU"    , nPosPH5)

	nP := Self:FindPH7(cChave)

	If Empty(np)
		Self:NewPH7() // inicialiaza array
		Self:NewCTR() // inicialiaza array
		np:= len(::aPH7)

		// Atualiza Chave
		Self:PutPH7("PH7_CONTRA", Self:GetPH5("PH5_CONTRA", nPosPH5), nP)
		Self:PutPH7("PH7_REVISA", Self:GetPH5("PH5_REVISA", nPosPH5), nP)
		Self:PutPH7("PH7_NUMERO", Self:GetPH5("PH5_NUMERO", nPosPH5), nP)
		Self:PutPH7("PH7_COMPET", Self:GetPH5("PH5_COMPET", nPosPH5), nP)
		Self:PutPH7("PH7_CONDIC", Self:GetPH5("PH5_CONDIC", nPosPH5), nP)
		Self:PutPH7("PH7_MOEDA" , Self:GetPH5("PH5_MOEDA" , nPosPH5), nP)
		Self:PutPH7("PH7_GU"    , Self:GetPH5("PH5_GU"    , nPosPH5), nP)

		Self:PutPH7("PH7_GRUPO" , Self:GetPH5("PH5_GRUPO" , nPosPH5), nP)
		Self:PutPH7("PH7_UNINEG", Self:GetPH5("PH5_UNINEG", nPosPH5), nP)
		Self:PutPH7("PH7_ANOMES", Self:GetPH5("PH5_ANOMES", nPosPH5), nP)
		Self:PutPH7("PH7_HROPER", Time()   , nP)
		Self:PutPH7("PH7_DTOPER", MsDate() , nP)
	EndIF

	Self:PutPH7("PH7_VLTOT" , Self:GetPH5("PH5_VLTOT" , nPosPH5), nP, .T.)
	Self:PutPH7("PH7_VLCANC", Self:GetPH5("PH5_VLCANC", nPosPH5), nP, .T.)
	Self:PutPH7("PH7_VLBONI", Self:GetPH5("PH5_VLBONI", nPosPH5), nP, .T.)
	Self:PutPH7("PH7_VLCARE", Self:GetPH5("PH5_VLCARE", nPosPH5), nP, .T.)
	Self:PutPH7("PH7_VLNOVA", Self:GetPH5("PH5_VLNOVA", nPosPH5), nP, .T.)
	Self:PutPH7("PH7_VLREAJ", Self:GetPH5("PH5_VLREAJ", nPosPH5), nP, .T.)
	Self:PutPH7("PH7_VLTRAN", Self:GetPH5("PH5_VLTRAN", nPosPH5), nP, .T.)
	Self:PutPH7("PH7_VLRFAT", Self:GetPH5("PH5_VLRFAT", nPosPH5), nP, .T.)
	Self:PutPH7("PH7_BUP"   , Self:GetPH5("PH5_BUP"   , nPosPH5), nP, .T.)
	Self:PutPH7("PH7_BDOWN" , Self:GetPH5("PH5_BDOWN" , nPosPH5), nP, .T.)
	Self:PutPH7("PH7_VLTRIB", Self:GetPH5("PH5_VLTRIB", nPosPH5), nP, .T.)
	Self:PutPH7("PH7_VLFATS", Self:GetPH5("PH5_VLFATS", nPosPH5), nP, .T.)
	Self:PutPH7("PH7_VLFAT2", Self:GetPH5("PH5_VLFAT2", nPosPH5), nP, .T.)
	Self:PutPH7("PH7_VLRAUD", Self:GetPH5("PH5_VLRAUD", nPosPH5), nP, .T.)
	Self:PutPH7("PH7_VLRTRC", Self:GetPH5("PH5_VLRTRC", nPosPH5), nP, .T.)
	Self:PutPH7("PH7_VLINCR", Self:GetPH5("PH5_VLINCR", nPosPH5), nP, .T.)
	Self:PutPH7("PH7_VLTRAE", Self:GetPH5("PH5_VLTRAE", nPosPH5), nP, .T.)
	Self:PutPH7("PH7_VARROY", Self:GetPH5("PH5_VARROY", nPosPH5), nP, .T.)
	Self:PutPH7("PH7_QTDREA", Self:GetPH5("PH5_QTDREA", nPosPH5), nP, .T.)
	Self:PutPH7("PH7_VLMULT", Self:GetPH5("PH5_VLMULT", nPosPH5), nP, .T.)
	Self:PutPH7("PH7_VLREA" , Self:GetPH5("PH5_VLREA" , nPosPH5), nP, .T.)
	Self:PutPH7("PH7_VLRBIL", Self:GetPH5("PH5_VLRBIL", nPosPH5), nP, .T.)
	Self:PutPH7("PH7_VARBIL", Self:GetPH5("PH5_VARBIL", nPosPH5), nP, .T.)
	Self:PutPH7("PH7_DLTTRC", Self:GetPH5("PH5_DLTTRC", nPosPH5), nP, .T.)
	Self:PutPH7("PH7_XVLRRE", Self:GetPH5("PH5_XVLRRE", nPosPH5), nP, .T.)
	Self:PutPH7("PH7_VLINTE", Self:GetPH5("PH5_VLINTE", nPosPH5), nP, .T.)
	Self:PutPH7("PH7_INTCAN", Self:GetPH5("PH5_INTCAN", nPosPH5), nP, .T.)


	Self:StatCTRL(nPosPH6, nP)

Return

Method StatCTRL(nPosPH6, nPosPH7) CLASS TGCVXC10
	Local cPH6STATUS := Self:GetPH6("PH6_STATUS", nPosPH6)

	Self:PutCTR("nTotal", 1, nPosPH7, .T.)
	If cPH6STATUS $ '00/01/02/03/04/05'
		Self:PutCTR("nFat"  , 1, nPosPH7, .T.)
	EndIf
	If cPH6STATUS $ '05'
		Self:PutCTR("nFatur", 1, nPosPH7, .T.)
	EndIf
	If cPH6STATUS $ '04'
		Self:PutCTR("nRefat", 1, nPosPH7, .T.)
	EndIf
	If cPH6STATUS $ '06'
		Self:PutCTR("nNotFat", 1, nPosPH7, .T.)
	EndIf
	If cPH6STATUS $ '07/08'
		Self:PutCTR("nNfCanc", 1, nPosPH7, .T.)
	EndIf
	If cPH6STATUS $ '00/01/02/03'
		Self:PutCTR("nAberto", 1, nPosPH7, .T.)
	EndIf
	If cPH6STATUS == "00"
		Self:PutCTR("nStat00", 1, nPosPH7, .T.)
	EndIf
	If cPH6STATUS == "01"
		Self:PutCTR("nStat01", 1, nPosPH7, .T.)
	EndIf
	If cPH6STATUS == "02"
		Self:PutCTR("nStat02", 1, nPosPH7, .T.)
	EndIf
	If cPH6STATUS == "03"
		Self:PutCTR("nStat03", 1, nPosPH7, .T.)
	EndIf
	If cPH6STATUS == "04"
		Self:PutCTR("nStat04", 1, nPosPH7, .T.)
	EndIf
	If cPH6STATUS == "05"
		Self:PutCTR("nStat05", 1, nPosPH7, .T.)
	EndIf
	If cPH6STATUS == "06"
		Self:PutCTR("nStat06", 1, nPosPH7, .T.)
	EndIf
	If cPH6STATUS == "07"
		Self:PutCTR("nStat07", 1, nPosPH7, .T.)
	EndIf
	If cPH6STATUS == "08"
		Self:PutCTR("nStat08", 1, nPosPH7, .T.)
	EndIf
Return

/*
PH7_STATUS 
Status	Descrição	                  No Passado e no Mês 	Futuro
00	Em Aberto                         VERMELHO              PRETO
01	Medição Parcialmente              VERMELHO              PRETO
02	Medição Gerada	                  VERMELHO              PRETO
03	Pedido Parcialmente	              VERMELHO              PRETO
04	Pedido Gerado	                  VERMELHO              PRETO
05	Represado Parcialmente            VERMELHO              PRETO
06	Represado Totalmente              VERMELHO              PRETO
07	Refaturado Parcialmente           VERMELHO              PRETO
08	Refaturado Totalmente             VERMELHO              PRETO
09	Faturado Parcialmente             VERMELHO              PRETO
10	Faturado                          AZUL                  AZUL
11	Sem valor a Faturar Parcialmente  VERMELHO              PRETO
12	Sem valor a Faturar Totalmente    AZUL                  AZUL
*/


Method StatPH7() CLASS TGCVXC10
	Local nx := 0
	Local cSitPH7 := ""

	Local nTotal  := 0
	Local nFat	  := 0
	Local nFatur  := 0
	Local nRefat  := 0
	Local nNotFat := 0
	Local nNfCanc := 0
	Local nAberto := 0
	Local nStat00 := 0
	Local nStat01 := 0
	Local nStat02 := 0
	Local nStat03 := 0
	Local nStat04 := 0
	Local nStat05 := 0
	Local nStat06 := 0
	Local nStat07 := 0
	Local nStat08 := 0


	For nx:= 1 to len(::aPH7)
		cSitPH7 := "00"
		nTotal  := Self:GetCTR("nTotal ", nx)
		nFat	:= Self:GetCTR("nFat"   , nx)
		nFatur  := Self:GetCTR("nFatur" , nx)
		nRefat  := Self:GetCTR("nRefat" , nx)
		nNotFat := Self:GetCTR("nNotFat", nx)
		nNfCanc := Self:GetCTR("nNfCanc", nx)
		nAberto := Self:GetCTR("nAberto", nx)
		nStat00 := Self:GetCTR("nStat00", nx)
		nStat01 := Self:GetCTR("nStat01", nx)
		nStat02 := Self:GetCTR("nStat02", nx)
		nStat03 := Self:GetCTR("nStat03", nx)
		nStat04 := Self:GetCTR("nStat04", nx)
		nStat05 := Self:GetCTR("nStat05", nx)
		nStat06 := Self:GetCTR("nStat06", nx)
		nStat07 := Self:GetCTR("nStat07", nx)
		nStat08 := Self:GetCTR("nStat08", nx)

		If nFat	== 0
			cSitPH7	:= "12"
		ElseIf (nFatur + nRefat + nNotFat + nNfCanc - nAberto) == nTotal
			cSitPH7	:= "10"
		Else
			If nStat05 >= (nStat00 + nStat01 + nStat02  + nStat03 + nStat04 + nStat06 + nStat07 + nStat08 ) //-- Faturado
				If (nStat05 + nRefat + nNotFat+ nNfCanc - nAberto) == nTotal
					cSitPH7:= "10"
				Else
					cSitPH7:= "09"
				EndIf
			ElseIf nStat04 >= (nStat00 + nStat01 + nStat02  + nStat03 + nStat05 + nStat06 + nStat07 + nStat08 )  //-- Refaturado
				If (nStat04 + nFatur + nNotFat + nNfCanc - nAberto) == nTotal
					cSitPH7:= "08"
				Else
					cSitPH7:= "07"
				EndIf
			ElseIf nStat03 >= (nStat00 + nStat01 + nStat02  + nStat04 + nStat05 + nStat06 + nStat07 + nStat08 )  //-- Represado
				If (nStat03 + nNotFat) == nTotal
					cSitPH7:= "06"
				Else
					cSitPH7:= "05"
				EndIf
			ElseIf nStat02 >= (nStat00 + nStat01 + nStat03 + nStat04  + nStat05 + nStat06 + nStat07 + nStat08 )  //-- Pedido Gerado
				If (nStat02 + nNotFat) == nTotal
					cSitPH7:= "04"
				Else
					cSitPH7:= "03"
				EndIf
			ElseIf nStat01 >= (nStat00 + nStat02 + nStat03 + nStat04  + nStat05 + nStat06 + nStat07 + nStat08 ) //-- Medição Gerada
				If (nStat01 + nNotFat) == nTotal
					cSitPH7:= "02"
				Else
					cSitPH7:= "01"
				EndIf
			ElseIf nStat06 >= (nStat00 + nStat01 + nStat02 + nStat03 + nStat04  + nStat05 + nStat07 + nStat08 ) //-- Sem Valor a Faturar
				If nStat06 == nTotal
					cSitPH7:= "12"
				Else
					cSitPH7:= "11"
				EndIf
			ElseIf nStat07 >= (nStat00 + nStat01 + nStat02 + nStat03 + nStat04  + nStat05 + nStat06 + nStat08 ) //-- NF Cancelada
				If nStat07 == nTotal
					cSitPH7:= "14"
				Else
					cSitPH7:= "13"
				EndIf
			ElseIf nStat00 >= (nStat00 + nStat01 + nStat02  + nStat04 + nStat05 + nStat06 + nStat07 + nStat08 )  //-- Represado
				If (nStat00 + nNotFat) == nTotal
					cSitPH7:= "00"
				Else
					cSitPH7:= "00"
				EndIf
			EndIF
		EndIf

		Self:PutPH7("PH7_STATUS", cSitPH7, nx)
	Next

Return


Method Calc() CLASS TGCVXC10
	Local nCNA     := 0
	Local nCNB     := 0
	Local oModCNA  := ::oModelo:GetModel("CNADETAIL")
	Local oModCNB  := ::oModelo:GetModel("CNBDETAIL")

	aSize(::aMensal, 0)

	If Empty(::cAMFim)
		::cAMFim   := Left(Dtos(date()), 6)
		::cCmpFim  := AMtoCmp(::cAMFim)
	EndIf

	CNTA300BlMd(oModCNB, .F.)

	For nCNA := 1 to oModCNA:Length()
		oModCNA:GoLine(nCNA)
		If oModCNA:IsDeleted()
			Loop
		EndIf
		For nCNB := 1 to oModCNB:Length()
			oModCNB:GoLine(nCNB)
			If oModCNB:IsDeleted()
				Loop
			EndIf
			If ! oModCNB:GetValue("CNB_SITUAC") $ "APCTOG"   //A=ATIVO,P=PENDENTE,C=CANCELADO,T=TRANSFERENCIA,O=TROCA
				Loop
			EndIf
			
			If ! IsBlind()
				IncProc("Processando Item: " + ::cContrato + " " + oModCNB:GetValue("CNB_NUMERO")  + " " + oModCNB:GetValue("CNB_ITEM") )
				ProcessMessage()
			EndIf
			If Empty(oModCNB:GetValue("CNB_NUMERO")) // TRATAMENTO ESPECIAL DEVIDO A INCLUSAO DE CONTRATOS
				oModCNB:LoadValue("CNB_NUMERO", oModCNA:GetValue("CNA_NUMERO"))
			EndIf

			Self:CheckMensal(oModCNB)

			Self:CalcItem(oModCNB)
			If ! Empty(::cMsgErro)
				Exit
			EndIf
		Next
		If ! Empty(::cMsgErro)
			Exit
		EndIf
	Next
	If Empty(::cMsgErro)
		Self:Refresh()   // atualiza modelo e e array de apoio no contrato
		// pendente atualizar os dados da classe item com os novos valores
	EndIf

Return


Method CheckMensal(oModCNB) CLASS TGCVXC10
	Local nPosProp := 0
	Local cPropos  := ""
	Local cCompet  := ""
	Local cItmProp := oModCNB:GetValue("CNB_PROITN")
	Local cProduto := oModCNB:GetValue("CNB_PRODUT")

	Begin Sequence
		If oModCNB:GetValue("CNB_TIPREC") <> "1"
			Break
		EndIf

		cPropos  := oModCNB:GetValue("CNB_PROPOS")


		If !IsMensal(cPropos, cItmProp, cProduto)
			Break
		EndIf

		cCompet := oModCNB:GetValue("CNB_XCOMPE")

		If Empty(cCompet)
			cIniFat := Left(DtoS(oModCNB:GetValue("CNB_DTSITU")), 6)
		Else
			cIniFat := CmptoAM(cCompet)
		EndIf


		nPosProp := Ascan(::aMensal, { |x| x[1] == cPropos })

		If nPosProp == 0
			AADD(::aMensal, {cPropos, cIniFat, {} } )
			nPosProp := Len(::aMensal)
		EndIf

		If ::aMensal[nPosProp, 2] > cIniFat
			::aMensal[nPosProp, 2] := cIniFat
		EndIf

		AADD(::aMensal[nPosProp, 3], {oModCNB:GetValue("CNB_NUMERO"), oModCNB:GetValue("CNB_ITEM")} )
	End Sequence
Return

Static Function SomaAM(cAm, nMes)
	Local cMes := ""
	Local cAno := ""
	Local ni   := 0

	For ni:= 1 to nMes
		cMes := Right(cAM, 2)
		cAno := Left(cAM, 4)

		If cMes == "12"
			cAM := soma1(cAno) + "01"
		Else
			cAM := soma1(cAM)
		EndIf
	Next
Return cAM





User Function CTRTrace(cConteudo,cConteudo2)
	Local nHandle2
	Local cArquivo := "CTR" + Right(Dtos(dDatabase), 6) + ".log"
	Local nDif
	Local cLog := ""
	Local aInfo   := GetUserInfoArray()
	Local np
	Local nMemory := 0

	DEFAULT cConteudo :=""
	Static nSeconds := 0

	If ! File(cArquivo)
		If (nHandle2 := MSFCreate(cArquivo,0)) == -1
			Return
		EndIf
	Else
		If (nHandle2 := FOpen(cArquivo,2)) == -1
			Return
		EndIf
	EndIf
	FSeek(nHandle2,0,2)
	If nSeconds==0
		nDif:=0
	Else
		nDif := Seconds()-nSeconds
	EndIf

	np := aScan(aInfo, {|x| x[3] == ThreadId()})
	If np >0
		nMemory := aInfo[np, 12]
	EndIF

	cLog := Alltrim(FunName()) + ":" + Alltrim(ProcName(1)) + "("+Alltrim(Str(ProcLine(1),5)) + ")"
	cLog += " Atual:" + Time() + " Diferenca:" + Str(nDif,8,2) + " Memoria:" + Alltrim(Str(nMemory))
	cLog += If(! Empty(cConteudo), " Obs: " + cConteudo, "") + CRLF
	FWrite(nHandle2, cLog)
	If cConteudo2<>NIL
		FWrite(nHandle2,VarInfo(cConteudo,cConteudo2,,.F.))
	EndIf

	nSeconds:= Seconds()
	FClose(nHandle2)

Return

Method CalcItem(oModCNB) CLASS TGCVXC10
	Local aArea       := GetArea()
	Local aAreaCN9    := CN9->(GetArea())
	Local aAreaCNB    := CNB->(GetArea())  // area utilizada para carregar a situação de todas as revisões
	Local aAreaSB1    := SB1->(GetArea())
	Local aAreaSBM    := SBM->(GetArea())
	Local aAreaSM0    := SM0->(GetArea())
	Local aAreaPI2    := PI2->(GetArea())
	Local aAreaCE1    := CE1->(GetArea())
	Local cNumero     := oModCNB:GetValue("CNB_NUMERO")
	Local cItem       := oModCNB:GetValue("CNB_ITEM")
	Local cProduto    := ""
	Local aSituac     := {}
	Local aCliente    := {}
	Local aExcecao    := {}
	Local aHistoric   := {}
	Local aPHG        := {}
	Local aCNCMain    := {}
	Local aCancela    := {}
	Local aCarenVnd   := {0,"",""}
	Local aCarencia   := {}
	Local aBonifica   := {}
	Local aReajuste   := {}
	Local aAjustFin   := {}
	Local aP68Intera  := {}
	Local aFinanceiro := {}
	Local aTransf     := {}
	Local aCtrlPer    := {}
	Local aBilling    := {}
	Local aRoyalties  := {}
	Local aTroca      := {}
	Local aReajExtra  := {}
	Local cProdAtu    := ""
	Local cContac     := ""
	Local cCodIss     := ""
	Local cEstCob     := ""
	Local cCodMun     := ""
	Local cUniNeg     := ""
	Local cGrpMI  	  := SuperGetMv( 'TI_#GRPMI',, '01|02|04' )
	Local nLimCmpProc := SuperGetMv('TI_LMCMPPRO', , 12)
	Local cAnoMesLim  := ""

	Local nx

	cAnoMesLim := SubStr(Dtos(monthsum(msdate(),nLimCmpProc)),1,6)

	If CmptoAM(self:cCmpFim) > cAnoMesLim
		cAnoMesLim := CmptoAM(self:cCmpFim)
	EndIf

	Begin Sequence

		CN9->(DbSetOrder(7))  //CN9_FILIAL+CN9_NUMERO+CN9_REVISA
		CN9->(DbSeek(xFilial("CNB") + ::cContrato + "05"))
		CNB->(DbSetOrder(1))
		If CNB->(DbSeek(xFilial("CNB") + ::cContrato + CN9->CN9_REVISA + cNumero + cItem))
			cProdAtu := CNB->CNB_PRODUT
		EndIf

		CNB->(DbSetOrder(3))  // CNB_FILIAL+CNB_CONTRA+CNB_NUMERO+CNB_ITEM + CNB_REVISA

		CNB->(DbSeek(xFilial("CNB") + ::cContrato + cNumero + cItem))
		CN9->(DbSetOrder(1))
		While CNB->(! Eof() .and. CNB_FILIAL + CNB_CONTRA + CNB_NUMERO + CNB_ITEM == xFilial("CNB") + ::cContrato + cNumero + cItem )
			If CNB->CNB_PRODUT <> cProdAtu
				CNB->(DbSkip())  // lixo na base
				Loop
			EndIf

			CN9->(DbSeek(xFilial("CN9") + CNB->CNB_CONTRA + CNB->CNB_REVISA))
			//aadd(aSituac, {Left(Dtos(CNB->CNB_DTSITU),6) , CNB->CNB_SITUAC, CNB->CNB_QUANT, CNB->CNB_VLUNIT, CNB->CNB_CONDIC, CNB->CNB_CONDPG, CNB->CNB_UNINEG, CNB->CNB_REVISA})
			aadd(aSituac, {Left(Dtos(CN9->CN9_DTREV),6) , CNB->CNB_SITUAC, CNB->CNB_QUANT, U_RoundUnid(CNB->CNB_VLUNIT,,CNB->CNB_UNINEG), CNB->CNB_CONDIC, CNB->CNB_CONDPG, CNB->CNB_UNINEG, CNB->CNB_REVISA}) 
			::cRevAtuDb:= CNB->CNB_REVISA
			CNB->(DbSkip())
		End

		CNB->(DbSetOrder(1))
		CNB->(DbSeek(xFilial("CNB") + ::cContrato + CN9->CN9_REVISA + cNumero + cItem))
		
		aadd(aSituac, { Left(Dtos(Date()),6)          , ;
			oModCNB:GetValue("CNB_SITUAC"), ;
			oModCNB:GetValue("CNB_QUANT" ), ;
			U_RoundUnid(oModCNB:GetValue("CNB_VLUNIT"),,oModCNB:GetValue("CNB_UNINEG")), ;
			oModCNB:GetValue("CNB_CONDIC"), ;
			oModCNB:GetValue("CNB_CONDPG"), ;
			oModCNB:GetValue("CNB_UNINEG"), ;
			oModCNB:GetValue("CNB_REVISA")})

		cProduto  := oModCNB:GetValue("CNB_PRODUT")

		SB1->(DbSetOrder(1))
		If ! SB1->(DbSeek(xFilial("SB1") + cProduto))
			::cMsgErro :="Produto não encontado!"
			Break
		EndIf
		cCodIss := SB1->B1_CODISS

		SBM->(DbSetOrder(1))
		If ! SBM->(DbSeek(xFilial("SBM") + SB1->B1_GRUPO))
			::cMsgErro := "Grupo não encontado!"
			Break
		EndIf

		cContac := SBM->BM_XCONTAC
		cGrupo  := oModCNB:GetValue("CNB_GRUPO")
		cUniNeg := oModCNB:GetValue("CNB_UNINEG")

		If ! SM0->(DbSeek(cGrupo + cUniNeg))
			::cMsgErro := " Nao Existe o Grupo | UniNeg [ "+ AllTrim( cGrupo ) +"' | "+ AllTrim( cUniNeg ) + " ]" + CRLF + "No Cadastro de Empresas do Sistema, Verifique!"
			Break
		EndIf

		cEstCob	:= SM0->M0_ESTCOB
		cCodMun	:= SubStr( SM0->M0_CODMUN, 3, 5 )

		If ! cEmpAnt $ cGrpMI .and. oModCNB:GetValue("CNB_SITUAC") $ "AP" 
			PI2->(dbSetOrder(3))
			If ! PI2->( MsSeek(xFilial( 'PI2' ) + cContac + oModCNB:GetValue("CNB_IMPOST")))
				::cMsgErro :=  'Não existe Conta Contábil cadastrada para o produto '+ AllTrim( cProduto ) + ' do Grupo ' + AllTrim( SB1->B1_GRUPO ) +' Modelo de Tributação:'+ AllTrim( oModCNB:GetValue("CNB_IMPOST") ) +' e Conta Contábil ' + AllTrim( cContac ) +'.'
				Break
			EndIf

			If ! CE1->( MsSeek( cUniNeg + cCodIss + cEstCob + cCodMun + cProduto))
				If !  CE1->(DbSeek( cUniNeg + cCodIss + cEstCob + cCodMun))
					::cMsgErro :=  'Nao Existe o Codigo de ISS [ '+ AllTrim( cCodIss )  +' ]'+ CRLF +'No Cadastro de Impostos do Sistema, Produto ['+ Alltrim(SB1->B1_COD) +']. Verifique!'
					Break
				EndIf
			EndIf
		EndIf

		Self:LoadCXL(aReajuste, aAjustFin)  // Ajuste de valores
		Self:LoadP68(aP68Intera)
		Self:LoadCli(aCliente, aExcecao, aHistoric, aPHG, aCNCMain)
		//Self:LoadPHG(aCliente)   // Estrutura de rateio
		Self:LoadPH3(aCancela)   // Programação, cancelamento e reativação
		Self:LoadPH4(aCarenVnd, aCarencia, aBonifica)  // Carencia na venda, Carencia pós venda e Bonificação
		Self:LoadPH8(aTransf)   //Historico das tranferencias
		Self:LoadPHD(aCtrlPer)  // Controle de faturamento de periodico
		Self:LoadPHM(aBilling)  // Carrega bilhetagem
		Self:LoadPHN(aTroca)    // Troca
		Self:LoadPQS(aReajExtra) // Reajuste Extraordinario
		Self:LoadPH5(aFinanceiro)

		If CmpToAM(Self:cCmpFim) > cAnoMesLim
			Self:cCmpFim := AMtoCmp(cAnoMesLim)
			Self:cAMFim  := cAnoMesLim
		EndIf

		Self:LoadCroFin( aCarenVnd)

		For nx:= 1 to len(aCliente)
			aRoyalties  := {}

			Self:LoadPNG(aCliente[nx], aRoyalties) // Carrega os royalties

			Self:CalcCroFin(aCliente, nx, aSituac, aCancela, aCarencia, aBonifica, aReajuste, aAjustFin, aP68Intera, aTransf, aFinanceiro, aCtrlPer, aBilling, aRoyalties, aTroca, aReajExtra, aExcecao, aHistoric, aPHG, aCNCMain)
			aSize(aRoyalties, 0)
		Next
		aSort(::aAMAtu)

	End Sequence

	RestArea(aAreaSM0)
	RestArea(aAreaPI2)
	RestArea(aAreaCE1)

	RestArea(aAreaSBM)
	RestArea(aAreaSB1)
	RestArea(aAreaCNB)
	RestArea(aAreaCN9)
	RestArea(aArea)
Return


Method LoadCXL(aReajuste, aAjustFin) CLASS TGCVXC10
	Local oModCXL   := ::oModelo:GetModel("CXLDETAIL")
	Local oModCNB   := ::oModelo:GetModel("CNBDETAIL")
	Local cNumero   := oModCNB:GetValue("CNB_NUMERO")
	Local cItem     := oModCNB:GetValue("CNB_ITEM")
	Local nCXL      := 0
	Local cXCompe   := ""
	Local nVlAnt    := 0
	Local nVlAtu    := 0
	Local cRevisa   := ""
	Local cIndice   := ""
	Local cDtReaj   := ""
	Local cNumRj    := ""
	Local nVlxComis := 0
	Local cAjustFin := GetMv("TI_CXLAJF")

	aReajuste := {}

	If oModCXL == NIL 
		Self:LoadDbCXL(aReajuste, aAjustFin)	
		Return 
	EndIf 

	For nCXL := 1 to oModCXL:Length()
		oModCXL:GoLine(nCXL)
		If oModCXL:IsDeleted()
			Loop
		EndIf
		If !(AllTrim(oModCXL:GetValue("CXL_SITUAC")) == "C")
			Loop
		EndIf
		If ! oModCXL:GetValue("CXL_PLAN") + oModCXL:GetValue("CXL_ITEMPL ") == cNumero + cItem
			Loop
		EndIf
		cXCompe  := oModCXL:GetValue("CXL_XCOMPE")
		nVlAnt   := oModCXL:GetValue("CXL_VLANT")
		nVlAtu   := oModCXL:GetValue("CXL_VLATU")
		cRevisa  := oModCXL:GetValue("CXL_REVISA")
		cIndice  := oModCXL:GetValue("CXL_INDICE")
		cDtReaj  := Dtos(oModCXL:GetValue("CXL_DTREAJ"))
		cNumRj   := oModCXL:GetValue("CXL_NUMRJ")
		nVlxComis:= oModCXL:GetValue("CXL_XCOMIS")

		aadd(aReajuste, { CmptoAM(cXCompe) , nVlAnt, nVlAtu, cRevisa, cIndice, cDtReaj, cNumRj})

		If Left(cIndice, 2) == "AJ" .OR. cIndice $ cAjustFin
			aadd(aAjustFin, {CmptoAM(cXCompe) , nVlAnt, nVlAtu, cRevisa, cIndice, cDtReaj, cNumRj, nVlxComis})
		EndIf
	Next

Return

Method LoadDbCXL(aReajuste, aAjustFin) CLASS TGCVXC10

    Local cChave    := ""
    Local aAreaCXL  := CXL->(GetArea("CXL"))
    Local cAjustFin := GetMv("TI_CXLAJF")

    cChave := xFilial("CXL") + CNB->(CNB_CONTRA + CNB_NUMERO + CNB_ITEM + "C")

    CXL->(DbOrderNickName("CXLCONTRA"))                                                                                                    
    CXL->(DbSeek(cChave))
    While CXL->(! Eof() .and. cChave == CXL_FILIAL + CXL_CONTRA + CXL_PLAN + CXL_ITEMPL + AllTrim(CXL_SITUAC)) 
        
        CXL->(aadd(aReajuste, {Right(CXL_XCOMPE, 4) + Left(CXL_XCOMPE, 2), CXL_VLANT, CXL_VLATU, CXL_REVISA, CXL_INDICE, Dtos(CXL_DTREAJ), CXL_NUMRJ}))

        If Left(CXL->CXL_INDICE, 2) == "AJ" .OR. CXL->CXL_INDICE $ cAjustFin
            CXL->(aadd(aAjustFin, {Right(CXL_XCOMPE, 4) + Left(CXL_XCOMPE, 2), CXL_VLANT, CXL_VLATU, CXL_REVISA, CXL_INDICE, Dtos(CXL_DTREAJ), CXL_NUMRJ, CXL_XCOMIS}))
        EndIf

        CXL->(DbSkip())
    End
    RestArea(aAreaCXL)
	
Return



Method LoadP68(aP68Intera) CLASS TGCVXC10
	Local oModP68   := ::oModelo:GetModel("P68DETAIL")
	Local oModCNB   := ::oModelo:GetModel("CNBDETAIL")
	Local cNumero   := oModCNB:GetValue("CNB_NUMERO")
	Local cItem     := oModCNB:GetValue("CNB_ITEM")
	Local nP68      := 0
	Local cAmProc   := ""
	Local nVlAnt    := 0
	Local nVlAtu    := 0
	Local cTipo     := ""
	Local cNumSeq   := ""
	Local cRevP68   := "" 
	aP68Intera := {}
	If oModP68 == NIL 
		Self:LoadDbP68(aP68Intera)
		Return 
	EndIf 
	For nP68 := 1 to oModP68:Length()
		oModP68:GoLine(nP68)
		If oModP68:IsDeleted()
			Loop
		EndIf
		If ! oModP68:GetValue("P68_PLAN") + oModP68:GetValue("P68_ITEMPL ") == cNumero + cItem
			Loop
		EndIf
		cAmProc  := oModP68:GetValue("P68_AMPROC")
		cTipo    := oModP68:GetValue("P68_TIPO"  )
		cNumSeq  := oModP68:GetValue("P68_NUMSEQ")
		nVlAnt   := oModP68:GetValue("P68_VLANT" )
		nVlAtu   := oModP68:GetValue("P68_VLATU" )
		cRevP68  := oModP68:GetValue("P68_REVISA" )
		aadd(aP68Intera, { cAmProc, cTipo, cNumSeq, nVlAnt, nVlAtu, cRevP68}) 
	Next

Return

Method LoadDbP68(aP68Intera) CLASS TGCVXC10
    Local cChave     := ""
    Local aAreaP68   := P68->(GetArea("P68"))
    

    cChave := FwxFilial("P68") + CNB->(CNB_CONTRA + CNB_NUMERO + CNB_ITEM)

    P68->(DbSetOrder(1))   //P68_FILIAL+P68_CONTRA+P68_PLAN+P68_ITEMPL+P68_AMPROC+P68_NUMSEQ                                                                                                 
    P68->(DbSeek(cChave))
    While P68->(! Eof() .and. cChave == P68_FILIAL + P68_CONTRA + P68_PLAN + P68_ITEMPL)       
        P68->(aadd(aP68Intera, {P68_AMPROC, P68_TIPO, P68_NUMSEQ, P68_VLANT, P68_VLATU, P68_REVISA}))
        P68->(DbSkip())
    End
    RestArea(aAreaP68)

Return

Method LoadCli(aCliente, aExcecao, aHistoric, aPHG, aCNCMain) CLASS TGCVXC10
	Local oModCNC   := ::oModelo:GetModel("CNCDETAIL")
	Local oModPHG   := ::oModelo:GetModel("PHGDETAIL")
	Local oModCNB   := ::oModelo:GetModel("CNBDETAIL")
	Local cNumero   := oModCNB:GetValue("CNB_NUMERO")
	Local cItem     := oModCNB:GetValue("CNB_ITEM")
	Local cCodCli   := ""
	Local cLojCli   := ""
	Local cDesCli   := ""
	Local nPerCli   := 0
	Local nCNC      := 0


	Default aCliente  := {}
	Default aExcecao  := {}
	Default aHistoric := {}
	Default aPHG      := {}
	Default aCNCMain  := {}


	For nCnc := 1 to oModCNC:Length()
		oModCNC:GoLine(nCnc)
		If oModCNC:IsDeleted()
			Loop
		EndIf
		If oModCNC:GetValue("CNC_TIPCLI") == "01"

			cCodCli  := oModCNC:GetValue("CNC_CLIENT")
			cLojCli  := oModCNC:GetValue("CNC_LOJACL")
			nPerCli  := 100

			cDesCli  := ""
			cDescCli := Posicione("SA1", 1, FwxFilial("SA1") + cCodCli + cLojCli, "SA1->A1_NREDUZ")

			aadd(aCliente, {cCodCli, cLojCli, cDescCli})
			AADD(aCNCMain, {cCodCli, cLojCli, "", "", nPerCli, "A", "CNC"})

			Exit
		EndIf
	Next


	LoadRateio(aCliente, aExcecao, aHistoric, aPHG, aCNCMain, ::cContrato, ::cRevisa, cNumero, cItem, oModPHG)

Return

Static Function LoadRateio(aCliente, aExcecao, aHistoric, aPHG, aCNCMain, cContrato, cRevisa, cNumero, cItem, oModPHG)

	Local cP70Excecao := "2"
	Local cP70Histori := "1"
	Local ny          := 0

	U_GCVA0947(aCliente, cP70Excecao, cContrato, cNumero, cItem, aExcecao)
	U_GCVA0947(aCliente, cP70Histori, cContrato, cNumero, cItem, aHistoric)
	

	If oModPHG ==NIL 
		LdDbPHG(aCliente, aPHG)
	Else 
		LoadPHG(aCliente, oModPHG, cNumero, cItem, aPHG)
		If MudouPHG(cContrato, cNumero, cItem, oModPHG)
			cAmUltFat := UltimoFatur(cContrato, cRevisa, cNumero, cItem)

			If Empty(cAmUltFat)
				cAmUltFat := TiraAM(Left(DTOS(dDataBase), 6), 1)
			Endif

			For ny:=1 to Len(aHistoric)
				If Empty(aHistoric[ny, 4])
					If aHistoric[ny, 3] > cAmUltFat
						aHistoric[ny, 6] := "I"
					Else
						aHistoric[ny, 4] := cAmUltFat
					Endif
				EndIf
			Next ny
		EndIf 
	EndIf

Return


Static Function MudouPHG(cContrato, cNumero, cItem, oModPHG)
	Local nPHG      := 0
	Local aP70      := {}
	Local aPHG      := {}
	Local cChave    := ""
	Local nTamAmAte := FwGetSx3Cache("P70_AMATE", "X3_TAMANHO")
	Local cTpP70    := "1"
	Local cCodCli   := ""
	Local cLojCli   := ""
	Local nPerCli   := ""
	Local cStatus   := "A"
	Local lRet      := .F.

	P70->(DbSetOrder(2)) //P70_FILIAL+P70_CONTRA+P70_NUMERO+P70_ITEM+P70_AMATE
	cChave := FwxFilial("P70") + cContrato + cNumero + cItem + Space(nTamAmAte)
	P70->(DbSeek(cChave))
	While P70->(!Eof()) .and. cChave == P70->(P70_FILIAL + P70_CONTRA + P70_NUMERO + P70_ITEM + P70_AMATE)
		If P70->(P70_STATUS + P70_TIPO) == cStatus + cTpP70
			P70->(AADD(aP70,{P70_CLIENT + P70_LOJA, P70_PERRAT}))
		EndIf
		P70->(DbSkip())
	End

	IF Empty(aP70)
		Return lRet
	Endif

	For nPHG := 1 to oModPHG:Length()
		oModPHG:GoLine(nPHG)
		If oModPHG:IsDeleted()
			Loop
		EndIf
		If ! oModPHG:GetValue("PHG_NUMERO") + oModPHG:GetValue("PHG_ITEM") == cNumero + cItem
			Loop
		EndIf

		cCodCli  := oModPHG:GetValue("PHG_CLIENT")
		cLojCli  := oModPHG:GetValue("PHG_LOJA")
		nPerCli  := oModPHG:GetValue("PHG_PERRAT")

		If Empty(cCodCli)
			Loop
		EndIf
		AADD(aPHG,{ cCodCli + cLojCli, nPerCli})
	Next

	Asort(aP70,,,{|x, y| x[1] < y[1]})
	Asort(aPHG,,,{|x, y| x[1] < y[1]})

	If Len(aP70) == Len(aPHG)

		For nPHG:= 1 to Len(aPHG)
			If !(aPHG[nPHg, 1] == aP70[nPHg, 1] .And. aPHG[nPHg, 2] == aP70[nPHg, 2])
				lRet := .T.
				Exit
			EndIf
		Next
	Else
		lRet := .T.
	EndIf


Return lRet

Static Function UltimoFatur(cContra, cRevisa, cNumero, cItem)

Return U_GCVA148U(cContra, cRevisa, cNumero, cItem)


Static Function LoadPHG(aCliente, oModPHG, cNumero, cItem, aPHG)
	Local nPHG    := 0
	Local cFilSA1 := FwxFilial("SA1")
	Local nPosCli := 0
	Local cCodCli := ""
	Local cLojCli := ""
	Local nPerCli := ""


	For nPHG := 1 to oModPHG:Length()
		oModPHG:GoLine(nPHG)
		If oModPHG:IsDeleted()
			Loop
		EndIf
		If ! oModPHG:GetValue("PHG_NUMERO") + oModPHG:GetValue("PHG_ITEM") == cNumero + cItem
			Loop
		EndIf

		cCodCli  := oModPHG:GetValue("PHG_CLIENT")
		cLojCli  := oModPHG:GetValue("PHG_LOJA")
		nPerCli  := oModPHG:GetValue("PHG_PERRAT")

		If Empty(cCodCli)
			Loop
		EndIf

		nPosCli := Ascan(aCliente, {|x| x[1] + x[2]== cCodCli + cLojCli })

		If nPosCli < 1
			cDescCli := Posicione("SA1", 1, cFilSA1 + cCodCli + cLojCli , "SA1->A1_NREDUZ")
			aadd(aCliente, {cCodCli, cLojCli, cDescCli})
			nPosCli := Len(aCliente)
		EndIf

		aadd(aPHG, {cCodCli, cLojCli, "", "", nPerCli, "A", "PHG"})
	Next
Return

Static Function LdDbPHG(aCliente, aPHG)
    Local cChave   := ""
    Local cDescCli := ""
    Local cFilSA1  := FwxFilial("SA1")
    Local nPosCli  := 0 

    PHG->(DbSetOrder(2)) //PHG_FILIAL + PHG_CONTRA + PHG_REVISA + PHG_NUMERO + PHG_ITEM
    cChave := xFilial("PHG") + CNB->(CNB_CONTRA + CNB_REVISA + CNB_NUMERO + CNB_ITEM )
    PHG->(DbSeek(cChave))
    While PHG->(! Eof() .and. cChave == PHG_FILIAL + PHG_CONTRA + PHG_REVISA + PHG_NUMERO + PHG_ITEM)

        nPosCli := Ascan(aCliente, {|x| x[1] + x[2]== PHG->(PHG_CLIENT + PHG_LOJA) })

        If nPosCli < 1
            cDescCli := Posicione("SA1", 1, cFilSA1 + PHG->(PHG_CLIENT + PHG_LOJA) , "SA1->A1_NREDUZ")
            PHG->(aadd(aCliente, {PHG_CLIENT, PHG_LOJA, cDescCli}))
            nPosCli := Len(aCliente)
        EndIf

        PHG->(aadd(aPHG, {PHG_CLIENT, PHG_LOJA, "", "", PHG_PERRAT, "A", "PHG"}))
        PHG->(DbSkip())
    End
    
Return


Method LoadPH3(aCancela) CLASS TGCVXC10
	Local cOper    := ""  //P-Programado, C-Cancelado, R-Reativado
	Local cTipo    := ""  //T-Total, P-Parcial
	Local dDtOper  := ctod("")
	Local cAMPH3   := ""
	Local cAMCob   := ""
	Local nQtdCNB  := 0
	Local nQtdOpe  := 0
	Local cCodMot  := ""
	Local nMulta   := 0
	Local oModPH3  := ::oModelo:GetModel("PH3DETAIL")
	Local nPH3     := 0
	Local oModCNB  := ::oModelo:GetModel("CNBDETAIL")
	Local cNumero  := oModCNB:GetValue("CNB_NUMERO")
	Local cItem    := oModCNB:GetValue("CNB_ITEM")
	Local cSitAnt  := ""

	aCancela := {}

	If oModPH3 == NIL 
		Self:LoadDbPH3(aCancela)
		Return 
	EndIf 

	For nPH3 := 1 to oModPH3:Length()
		oModPH3:GoLine(nPH3)
		If oModPH3:IsDeleted()
			Loop
		EndIf

		If ! oModPH3:GetValue("PH3_NUMERO") + oModPH3:GetValue("PH3_ITEM") == cNumero + cItem
			Loop
		EndIf

		nQtdCNB  := 0
		nQtdOpe  := 0
		cAMPH3   := ""
		cAMCob   := ""
		cOper    := oModPH3:GetValue("PH3_STATUS")

		If cOper == "P"
			nQtdCNB  := oModPH3:GetValue("PH3_QUANT")
			nQtdOpe  := oModPH3:GetValue("PH3_QTDCAN")
			cAMPH3   := CmptoAM(oModPH3:GetValue("PH3_CMPCAN"))
			If cAMPH3 > Left(Dtos(Date()), 6)
				If cAMPH3 > ::cAMFim
					::cCmpFim  := oModPH3:GetValue("PH3_CMPCAN")
					::cAMFim   := cAMPH3
				EndIf
			EndIf
			cAMCob   := ""
		ElseIf cOper == "C"
			nQtdCNB  := oModPH3:GetValue("PH3_QUANT")
			nQtdOpe  := oModPH3:GetValue("PH3_QTDCAN")
			cAMPH3   := CmptoAM(oModPH3:GetValue("PH3_CMPCAN"))
			cAMCob   := ""
			If cAMPH3 > ::cAMFim
				::cCmpFim  := oModPH3:GetValue("PH3_CMPCAN")
				::cAMFim   := cAMPH3
			EndIf

			//Loop // ignorando a ph3 e utilizando somente os registros da Revisão
		ElseIf cOper == "R"
			nQtdCNB := 0
			nQtdOpe := oModPH3:GetValue("PH3_QUANT")
			cAMCob  := CmptoAM(oModPH3:GetValue("PH3_CMPCOB"))
			cAMPH3  := CmptoAM(oModPH3:GetValue("PH3_CMPREA"))
			If ! Empty(oModPH3:GetValue("PH3_CMPCAN"))
				Loop
			EndIf
			If Empty(cAMCob)
				cAMCob := cAMPH3
			EndIF
			If cAMPH3 > ::cAMFim
				::cCmpFim  := oModPH3:GetValue("PH3_CMPREA")
				::cAMFim   := cAMPH3
			EndIf
			//Loop // ignorando a ph3 e utilizando somente os registros da Revisão
		EndIf
		cTipo   :=  If(nQtdCNB == nQtdOpe, "T", "P")
		dDtOper := oModPH3:GetValue("PH3_DATA")
		cCodMot := oModPH3:GetValue("PH3_MOTBC")
		nMulta  := oModPH3:GetValue("PH3_VLMULT")
		cSitAnt := oModPH3:GetValue("PH3_SITUAC")

		aadd(aCancela, {cAMPH3, cOper, cTipo, dDtOper, nQtdOpe, cCodMot, nMulta, cAMCob, cSitAnt})

	Next
	aSort(aCancela,,, {|x,y| x[1] + x[3] < y[1] + y[3] })

Return

Method LoadDbPH3(aCancela) CLASS TGCVXC10

    Local aAreaPH3 := PH3->(GetArea("PH3"))
    Local cChave   := ""
    Local cOper    := ""  //P-Programado, C-Cancelado, R-Reativado
    Local cTipo    := ""  //T-Total, P-Parcial
    Local dDtOper  := ctod("")
    Local cAMPH3   := ""
	Local cAMCob   := ""
    Local nQtdCNB  := 0
    Local nQtdOpe  := 0
    Local cCodMot  := ""
    Local cSitAnt  := ""
    Local nMulta   := 0
    
    
    PH3->(DbSetOrder(1)) //PH3_FILIAL+PH3_CONTRA+PH3_REVISA+PH3_NUMERO+PH3_PRODUT+PH3_ITEM+PH3_STATUS+PH3_CMPSOL+PH3_CMPCAN+PH3_ITSEQ 
    cChave := xFilial("PH3") + CNB->(CNB_CONTRA + CNB_REVISA + CNB_NUMERO + CNB_PRODUT + CNB_ITEM )
    PH3->(DbSeek(cChave))
    While PH3->(! Eof() .and. cChave == PH3_FILIAL + PH3_CONTRA + PH3_REVISA + PH3_NUMERO + PH3_PRODUT + PH3_ITEM)
        cOper    :=PH3->PH3_STATUS 
        If cOper == "P"
            nQtdCNB  := PH3->PH3_QUANT
            nQtdOpe  := PH3->PH3_QTDCAN
            cAMPH3   := CmptoAM(PH3->PH3_CMPCAN)
            If cAMPH3 > Left(Dtos(Date()), 6)
                If cAMPH3 > CmptoAM(::cCmpFim)
                    ::cCmpFim := AMtoCmp(cAMPH3)
					::cAMFim   := cAMPH3
                EndIf
            EndIf
            cAMCob := ""
        ElseIf cOper == "C" 
            nQtdCNB  := PH3->PH3_QUANT
            nQtdOpe  := PH3->PH3_QTDCAN
            cAMPH3   := CmptoAM(PH3->PH3_CMPCAN)
            cAMCob   := "" 
            If cAMPH3 > CmptoAM(::cCmpFim)
                ::cCmpFim := AMtoCmp(cAMPH3)
				::cAMFim   := cAMPH3
            EndIf
        ElseIf cOper == "R" 
            nQtdCNB := 0
            nQtdOpe := PH3->PH3_QUANT
            cAMCob  := CmptoAM(PH3->PH3_CMPCOB)   
            cAMPH3  := CmptoAM(PH3->PH3_CMPREA)
            If ! Empty(PH3->PH3_CMPCAN)
                PH3->(DbSkip())        
                Loop
            EndIf
            If Empty(cAMCob)
                cAMCob := cAMPH3
            EndIF
            If cAMPH3 > CmptoAM(::cCmpFim)
                ::cCmpFim := AMtoCmp(cAMPH3)
				::cAMFim   := cAMPH3
            EndIf
        EndIf
        cTipo   :=  If(nQtdCNB == nQtdOpe, "T", "P")
        dDtOper := PH3->PH3_DATA
        cCodMot := PH3->PH3_MOTBC        
        nMulta  := PH3->PH3_VLMULT
        cSitAnt := PH3->PH3_SITUAC
        aadd(aCancela, {cAMPH3, cOper, cTipo, dDtOper, nQtdOpe, cCodMot, nMulta, cAMCob, cSitAnt })
        PH3->(DbSkip())
    End
    

    aSort(aCancela,,, {|x,y| x[1] + x[3] < y[1] + y[3] })

    RestArea(aAreaPH3)

Return 




Method LoadPH4(aCarenVnd, aCarencia, aBonifica) CLASS TGCVXC10
	Local aAreaPH4 := PH4->(GetArea("PH4"))
	Local nCaren   := 0
	Local cCarIni  := ""
	Local cCarFin  := ""
	Local cAMOper  := ""
	Local dDtOper  := ctod("")
	Local cCliente := ""
	Local cLoja    := ""
	Local cTpDesc  := ""
	Local nVlrDes  := 0
	Local nPerDes  := 0
	Local aAux     := {}
	Local cAMIni   := ""
	Local oModCNB  := ::oModelo:GetModel("CNBDETAIL")
	Local cNumero  := oModCNB:GetValue("CNB_NUMERO")
	Local cItem    := oModCNB:GetValue("CNB_ITEM")

	Local oModPH4  := ::oModelo:GetModel("PH4DETAIL")
	Local nPH4     := 0

	Local dVigIni  := oModCNB:GetValue("CNB_VIGINI")
	Local dDtSitu  := oModCNB:GetValue("CNB_DTSITU")
	Local lPrincipal := .F.
	Local nCnc       := 0
	Local cCliPrin   := ""
	Local cLojPrin   := ""
	Local oModCNC  := ::oModelo:GetModel("CNCDETAIL")

	cAMIni := Left(Dtos(dVigIni), 6)
	If Empty(cAMIni)
		cAMIni := Left(Dtos(dDtSitu), 6)
	EndIf


	For nCnc := 1 to oModCNC:Length()
		oModCNC:GoLine(nCnc)
		If oModCNC:IsDeleted()
			Loop
		EndIf
		If oModCNC:GetValue("CNC_TIPCLI") == "01"
			cCliPrin  := oModCNC:GetValue("CNC_CLIENT")
			cLojPrin  := oModCNC:GetValue("CNC_LOJACL")
			Exit
		EndIf
	Next

	If oModPH4 == NIL 
		Self:LoadDBPH4(aCarenVnd, aCarencia, aBonifica, cCliPrin, cLojPrin)
		Return  
	EndIf 


	For nPH4 := 1 to oModPH4:Length()
		oModPH4:GoLine(nPH4)
		If oModPH4:IsDeleted()
			Loop
		EndIf
		If oModPH4:GetValue("PH4_TIPO") <> "C"
			Loop
		EndIf
		If oModPH4:GetValue("PH4_STATUS") == "I"
			Loop
		EndIf
		If ! oModPH4:GetValue("PH4_NUMERO") + oModPH4:GetValue("PH4_ITEM") == cNumero + cItem
			Loop
		EndIf
		cAMOper := Left(Dtos(oModPH4:GetValue("PH4_DTOPER")), 6)
		cCarIni := oModPH4:GetValue("PH4_CMPINI")
		cCarFin := oModPH4:GetValue("PH4_CMPFIM")
		dDtOper := oModPH4:GetValue("PH4_DTOPER")
		cCliente:= oModPH4:GetValue("PH4_CLIENT")
		cLoja   := oModPH4:GetValue("PH4_LOJA")

		lPrincipal :=cCliPrin + cLojPrin == cCliente + cLoja

		aadd(aAux, {cAMOper, cCarIni, cCarFin, dDtOper, cCliente, cLoja, lPrincipal})

	Next

	If Len(aAux) > 0
		aSort(aAux,,, {|x, y| x[4] < y[4] })
		If aAux[1, 2] == Strzero(Month(dDtSitu), 2) + "/" + Strzero(Year(dDtSitu), 4)
			cCarIni := aAux[1, 2]
			cCarFin := aAux[1, 3]
			If cAMIni == CmptoAM(cCarIni)
				nCaren  := AMCalcDif(cCarIni, cCarFin)
			ElseIf cAMIni > CmptoAM(cCarIni) .and. cAMIni <= CmptoAM(cCarFin)
				nCaren  := AMCalcDif(AMtoCmp(cAMIni), cCarFin) + 1
			EndIf
		EndIf
	EndIf
	aCarenVnd := {nCaren, cCarIni, cCarFin}
	aCarencia := aClone(aAux)

	aAux:= {}
	For nPH4 := 1 to oModPH4:Length()
		oModPH4:GoLine(nPH4)
		If oModPH4:IsDeleted()
			Loop
		EndIf
		If oModPH4:GetValue("PH4_TIPO") <> "B"
			Loop
		EndIf
		If oModPH4:GetValue("PH4_STATUS") == "I"
			Loop
		EndIf
		If ! oModPH4:GetValue("PH4_NUMERO") + oModPH4:GetValue("PH4_ITEM") == cNumero + cItem
			Loop
		EndIf
		cAMOper  := Left(Dtos(oModPH4:GetValue("PH4_DTOPER")), 6)
		cCarIni  := oModPH4:GetValue("PH4_CMPINI")
		cCarFin  := oModPH4:GetValue("PH4_CMPFIM")
		dDtOper  := oModPH4:GetValue("PH4_DTOPER")
		cCliente := oModPH4:GetValue("PH4_CLIENT")
		cLoja    := oModPH4:GetValue("PH4_LOJA")
		cTpDesc  := oModPH4:GetValue("PH4_TPDESC")
		nVlrDes  := oModPH4:GetValue("PH4_VLRDES")
		nPerDes  := oModPH4:GetValue("PH4_PERDES")
		aadd(aAux, {cAMOper, cCarIni, cCarFin, dDtOper, cCliente, cLoja, cTpDesc, nVlrDes, nPerDes})

	Next
	aBonifica := aClone(aAux)
	RestArea(aAreaPH4)

Return

Method LoadDbPH4(aCarenVnd, aCarencia, aBonifica, cCliPrin, cLojPrin ) CLASS TGCVXC10
    Local aAreaPH4  := PH4->(GetArea("PH4"))
    Local cChave    := ""
    Local nCaren    := 0
    Local cCarIni   := ""
    Local cCarFin   := ""
    Local dDtOper   := ctod("")
    Local cCliente  := ""
    Local cLoja     := ""
    Local lPrincipal:= .F.
    Local cTpDesc   := ""
    Local nVlrDes   := ""
    Local nPerDes   := ""
    Local cAMOper   := ""
    Local cAMIni    := ""
    Local aAux      := {}
    
    cAMIni := Left(Dtos(CNB->CNB_VIGINI), 6)
    If Empty(cAMIni)
        cAMIni := Left(Dtos(CNB->CNB_DTSITU), 6)
    EndIf

    PH4->(DbSetOrder(1))  //PH4_FILIAL+PH4_CONTRA+PH4_REVISA+PH4_NUMERO+PH4_ITEM+PH4_PRODUT+PH4_TIPO+PH4_STATUS+PH4_CMPINI+PH4_CMPFIM+PH4_ITSEQ                                             
    cChave := xFilial("PH4") + CNB->(CNB_CONTRA + CNB_REVISA + CNB_NUMERO + CNB_ITEM + CNB_PRODUT) + "C"  //B=BonificOper;C=Carencia
    PH4->(DbSeek(cChave))
    // necessario criar laço devido o campo PH4_CMPINI, que está colando fora da ordem
    While PH4->(! Eof() .and. PH4_FILIAL+PH4_CONTRA+PH4_REVISA+PH4_NUMERO+PH4_ITEM+PH4_PRODUT+PH4_TIPO == cChave)
        If PH4->PH4_STATUS == "I"
            PH4->(DbSkip())
            Loop
        EndIf
        cAMOper  := Left(Dtos(PH4->PH4_DTOPER), 6)
        cCarIni  := PH4->PH4_CMPINI
        cCarFin  := PH4->PH4_CMPFIM
        dDtOper  := PH4->PH4_DTOPER
        cCliente := PH4->PH4_CLIENT 
        cLoja    := PH4->PH4_LOJA 

        
        lPrincipal := cCliPrin + cLojPrin == cCliente + cLoja

        aadd(aAux, {cAMOper, cCarIni, cCarFin, dDtOper, cCliente, cLoja, lPrincipal})
        
        PH4->(DbSkip())
    End
    
    If Len(aAux) > 0
        aSort(aAux,,, {|x, y| x[4] < y[4] })
        If aAux[1, 2] == Strzero(Month(CNB->CNB_DTSITU), 2) + "/" + Strzero(Year(CNB->CNB_DTSITU), 4)
            cCarIni := aAux[1, 2]
            cCarFin := aAux[1, 3]
            If cAMIni == CmptoAM(cCarIni) 
                nCaren  := AMCalcDif(cCarIni, cCarFin)
            ElseIf cAMIni > CmptoAM(cCarIni) .and. cAMIni <= CmptoAM(cCarFin)
                nCaren  := AMCalcDif(AMtoCmp(cAMIni), cCarFin) + 1
            EndIf
        EndIf
    EndIf
    aCarenVnd := {nCaren, cCarIni, cCarFin}
    aCarencia := aClone(aAux)

    aSize(aAux, 0)
    aAux := {}
    PH4->(DbSetOrder(1))  //PH4_FILIAL+PH4_CONTRA+PH4_REVISA+PH4_NUMERO+PH4_ITEM+PH4_PRODUT+PH4_TIPO+PH4_STATUS+PH4_CMPINI+PH4_CMPFIM+PH4_ITSEQ                                             
    cChave := xFilial("PH4") + CNB->(CNB_CONTRA + CNB_REVISA + CNB_NUMERO + CNB_ITEM + CNB_PRODUT) + "B" 
    PH4->(DbSeek(cChave))
    // necessario criar laço devido o campo PH4_CMPINI, que está colando fora da ordem
    While PH4->(! Eof() .and. PH4_FILIAL+PH4_CONTRA+PH4_REVISA+PH4_NUMERO+PH4_ITEM+PH4_PRODUT+PH4_TIPO == cChave)
        If PH4->PH4_STATUS == "I"
            PH4->(DbSkip())
            Loop
        EndIf
        cAMOper  := Left(Dtos(PH4->PH4_DTOPER), 6)
        cCarIni  := PH4->PH4_CMPINI
        cCarFin  := PH4->PH4_CMPFIM
        dDtOper  := PH4->PH4_DTOPER
        cCliente := PH4->PH4_CLIENT 
        cLoja    := PH4->PH4_LOJA 
        cTpDesc  := PH4->PH4_TPDESC 
        nVlrDes  := PH4->PH4_VLRDES 
        nPerDes  := PH4->PH4_PERDES 
        aadd(aAux, {cAMOper, cCarIni, cCarFin, dDtOper, cCliente, cLoja, cTpDesc, nVlrDes, nPerDes})
 
        PH4->(DbSkip())
    End
    aBonifica := aClone(aAux)

    RestArea(aAreaPH4)

Return  


Method LoadPH8(aTransf) CLASS TGCVXC10
	Local oModPH8  := ::oModelo:GetModel("PH8DETAIL")
	Local nPH8     := 0
	Local cAMOper  := ""
	Local cCompet  := ""
	Local cTM      := ""
	Local cCondic  := ""
	Local cPropos  := ""
	Local cItmPro  := ""
	Local nQtdTrf  := 0
	Local cCtrOri  := ""
	Local cCtrDes  := ""
	Local oModCNB  := ::oModelo:GetModel("CNBDETAIL")
	Local cNumero  := oModCNB:GetValue("CNB_NUMERO")
	Local cItem    := oModCNB:GetValue("CNB_ITEM")
	Local nQtdOri  := 0
	Local np

	If oModPH8 == NIL
		Self:LoadDbPH8(aTransf)
		Return 
	EndIF 	

	For nPH8 := 1 to oModPH8:Length()
		oModPH8:GoLine(nPH8)
		If oModPH8:IsDeleted()
			Loop
		EndIf

		cCompet := oModPH8:GetValue("PH8_COMPET")
		//cCompet := RIGHT(DtoC(oModPH8:GetValue("PH8_DTOPER")),7)
		cTM     := AllTrim(oModPH8:GetValue("PH8_TM"))
		cCondic := oModPH8:GetValue("PH8_CONDIC")
		cPropos := oModPH8:GetValue("PH8_PROPOS")
		cItmPro := oModPH8:GetValue("PH8_ITMPRO")
		nQtdTrf := oModPH8:GetValue("PH8_QTDTRF")
		cCtrOri := oModPH8:GetValue("PH8_CTRORI")
		cCtrDes := oModPH8:GetValue("PH8_CTRDES")
		nQtdOri := oModPH8:GetValue("PH8_QTDORI")

		If ! oModPH8:GetValue("PH8_NUMERO") + oModPH8:GetValue("PH8_ITEM") == cNumero + cItem
			Loop
		EndIf
		cAMOper := CmptoAM(cCompet)
		np:= Ascan(aTransf, {|x| x[1] + x[7] + x[8] == cAMOper + cCtrOri + cCtrDes })
		If Empty(np)
			aadd(aTransf, {cAMOper, cTM, cCondic, cPropos, cItmPro, nQtdTrf, cCtrOri, cCtrDes, nQtdOri, 0 })
			np:= Len(aTransf)
		EndIf

		If cTM == "E"
			aTransf[np, 10 ] += nQtdTrf
		else
			aTransf[np, 10 ] -= nQtdTrf
		EndIf

	Next
	aSort(aTransf,,, {|x,y| x[1] < y[1]} )

Return

Method LoadDbPH8(aTransf) CLASS TGCVXC10

    Local aAreaPH8 := PH8->(GetArea("PH8"))
    Local cChave   := ""
    Local nP       := 0
    
    Local cCompet  := ""
    Local cTM      := ""
    Local cCondic  := ""
    Local cPropos  := ""
    Local cItmPro  := ""
    Local nQtdTrf  := 0
    Local cCtrOri  := ""
    Local cCtrDes  := ""
    Local nQtdOri  := ""
    Local aTransf  := {}
        
    PH8->(DbSetOrder(3))   //PH8_FILIAL+PH8_CONTRA+PH8_NUMERO+PH8_ITEM                                   
    cChave := xFilial("PH8") + CNB->(CNB_CONTRA +  CNB_NUMERO + CNB_ITEM) 
    PH8->(DbSeek(cChave))
    // necessario criar laço devido o campo PH8_CMPINI, que está colando fora da ordem
    While PH8->(! Eof() .and. PH8_FILIAL + PH8_CONTRA +  PH8_NUMERO + PH8_ITEM == cChave)
        cCompet := PH8->PH8_COMPET
        //cCompet := RIGHT(DtoC(PH8->PH8_DTOPER),7)
        cTM     := Left(PH8->PH8_TM, 1)
        cCondic := PH8->PH8_CONDIC
        cPropos := PH8->PH8_PROPOS
        cItmPro := PH8->PH8_ITMPRO
        nQtdTrf := PH8->PH8_QTDTRF
        cCtrOri := PH8->PH8_CTRORI
        cCtrDes := PH8->PH8_CTRDES
        nQtdOri := PH8->PH8_QTDORI
        cAMOper := CmptoAM(cCompet)

        np:= Ascan(aTransf, {|x| x[1] + x[7] + x[8] == cAMOper + cCtrOri + cCtrDes })
        If Empty(np)
            aadd(aTransf, {cAMOper, cTM, cCondic, cPropos, cItmPro, nQtdTrf, cCtrOri, cCtrDes, nQtdOri, 0 })
            np:= Len(aTransf) 
        EndIf

        If cTM == "E"
            aTransf[np, 10 ] += nQtdTrf
        else
            aTransf[np, 10 ] -= nQtdTrf
        EndIf

        PH8->(DbSkip())
    End
    RestArea(aAreaPH8)
    aSort(aTransf,,, {|x,y| x[1] < y[1]} ) 
    
Return 


Method LoadPHD(aCtrlPer) CLASS TGCVXC10
	Local oModCNB  := ::oModelo:GetModel("CNBDETAIL")
	Local cNumero  := oModCNB:GetValue("CNB_NUMERO")
	Local cItem    := oModCNB:GetValue("CNB_ITEM")
	Local cChave := ""
	Local aAreaPHD := PHD->(GetArea())

	aCtrlPer := {}
	cChave := xFilial("PHD") + ::cContrato + ::cRevisa

	PHD->(DbSetOrder(2))
	PHD->(DbSeek(cChave))
	While PHD->(!Eof())
		If  xFilial("PHD") + ::cContrato + cNumero + cItem + ::cRevisa == PHD->(PHD_FILIAL + PHD_CONTRA + PHD_NUMERO + PHD_ITEM + PHD_REVISA)
			PHD->(aadd(aCtrlPer, {CmpToAM(PHD_COMPET), PHD_REVISA}))
		Endif
		PHD->(DbSkip())
	End

	If Len(aCtrlPer) > 0
		aSort(aCtrlPer,,, {|x,y| x[2] < y[2] })
	EndIf
	RestArea(aAreaPHD)

Return



Method LoadPHM(aBilling) CLASS TGCVXC10
	Local oModCNB  := ::oModelo:GetModel("CNBDETAIL")
	Local cNumero  := oModCNB:GetValue("CNB_NUMERO")
	Local cItem    := oModCNB:GetValue("CNB_ITEM")
	Local cChave := ""
	Local aAreaPHM := PHM->(GetArea())

	aBilling := {}
	If CNB->CNB_XBILLI <> '1'
		Return
	EndIf

	If CNB->CNB_XTPCNT  == '1'
		Return
	EndIf

	cChave := xFilial("PHM") + ::cContrato + cNumero + cItem

	PHM->(DbSetOrder(3))
	PHM->(DbSeek(cChave))
	While PHM->(! Eof() .and. cChave == PHM_FILIAL + PHM_CONTRA + PHM_NUMERO + PHM_ITEM)
		PHM->(aadd(aBilling, {CmpToAM(PHM_CMPFAT), PHM_IDBILL, PHM_VLTOT, PHM_QUANT, PHM_VARIAV, Recno()}))
		PHM->(DbSkip())
	End
	If Len(aBilling) > 0
		aSort(aBilling,,, {|x,y| x[1] < y[1] })
	EndIf
	RestArea(aAreaPHM)

Return

Method LoadPHN(aTroca) CLASS TGCVXC10
	Local oModPHN   := ::oModelo:GetModel("PHNDETAIL")
	Local oModCNB   := ::oModelo:GetModel("CNBDETAIL")
	Local cNumero   := oModCNB:GetValue("CNB_NUMERO")
	Local cItem     := oModCNB:GetValue("CNB_ITEM")
	Local nPHN      := 0
	Local cAMTroca  := ""
	Local nDeltaTrc := 0
	Local cIdDltTrc := ""
	Local cTpTroca  := ""
	Local nVlrTrc   := 0
	Local nQtdTroca := 0
	Local cSitPHN   := 0
	Local cAMPHN    := ""

	If oModPHN == NIL 
		Self:LoadDbPHN(aTroca)
		Return 
	EndIf 

	For nPHN := 1 to oModPHN:Length()
		oModPHN:GoLine(nPHN)
		If oModPHN:IsDeleted()
			Loop
		EndIf

		If ! oModPHN:GetValue("PHN_NUMERO") + oModPHN:GetValue("PHN_ITEM") == cNumero + cItem
			Loop
		EndIf
		If ! oModPHN:GetValue("PHN_VIGENT") == "A"
			Loop
		EndIf

		cAMTroca  := oModPHN:GetValue("PHN_AMSCRF")
		nDeltaTrc := oModPHN:GetValue("PHN_DELTPR")
		cIdDltTrc := oModPHN:GetValue("PHN_IDPROC")
		cTpTroca  := oModPHN:GetValue("PHN_TIPO"  )
		nVlrTrc   := oModPHN:GetValue("PHN_VLRCTR")
		nQtdTroca := oModPHN:GetValue("PHN_QTTROC")
		cSitPHN   := oModPHN:GetValue("PHN_SITUAC")
		cAMPHN    := oModPHN:GetValue("PHN_AMSTRC")

		aadd(aTroca, {cAMTroca, nDeltaTrc, cIdDltTrc, cTpTroca, nVlrTrc, nQtdTroca, cSitPHN, cAMPHN,  0})
		If cAMTroca > ::cAMFim
			::cCmpFim  := AMtoCmp(cAMTroca)
			::cAMFim   := cAMTroca
		EndIf

	Next
	aSort(aTroca,,, {|x,y| x[1] < y[1]} )

Return


Method LoadDbPHN(aTroca) CLASS TGCVXC10
    Local cChave   := ""
    Local aAreaPHN := PHN->(GetArea("PHN"))
    Local cAnoMes  := ""
    Local aTroca   := {}

    cChave := xFilial("PHN") + CNB->(CNB_CONTRA + CNB_REVISA + CNB_NUMERO + CNB_ITEM )
    PHN->(DbSetOrder(5)) // PHN_FILIAL+PHN_CONTRA+PHN_REVISA+PHN_NUMERO+PHN_ITEM+PHN_VIGENT+PHN_AMSCRF                                                                                                     
    PHN->(DbSeek(cChave))
    While PHN->(! Eof() .and. cChave == PHN_FILIAL + PHN_CONTRA + PHN_REVISA +  PHN_NUMERO + PHN_ITEM) 
        If ! PHN->PHN_VIGENT == "A"
            PHN->(DbSkip())
            Loop
        EndIf
        cAnoMes := PHN->PHN_AMSCRF
        PHN->(aadd(aTroca, {PHN_AMSCRF, PHN_DELTPR, PHN_IDPROC, PHN_TIPO, PHN_VLRCTR, PHN_QTTROC, PHN_SITUAC, PHN_AMSTRC, Recno()})) 
        
        If cAnoMes > CmptoAM(::cCmpFim)
            ::cCmpFim  := AMtoCmp(cAnoMes)
			::cAMFim   := cAnoMes
        EndIf

        PHN->(DbSkip())
    End
    If Len(aTroca) > 0
        aSort(aTroca,,, {|x,y| x[1] < y[1] })
    EndIf

    RestArea(aAreaPHN)

Return

Method LoadPQS(aReajExtra) CLASS TGCVXC10
	Local cChave   := ""
	Local aAreaPQS := PQS->(GetArea())
	Local lCalc  := SuperGetMv("TI_RJTEXT",,.T.)
	Local cAMIni    := ""
	Local cSeq      := ""
	Local cLinRec   := ""
	Local nPerc     := 0
	Local dDtLim    := ctod("")
	Local cAMFim    := ""
	Local cCorp     := ""
	Local cFase     := ""
	Local cObs      := ""

	aReajExtra := {}
	If ! lCalc
		Return
	EndIf

	PQS->(DbSetOrder(1)) //PQS_FILIAL+PQS_CONTRA+PQS_COMPET+PQS_SEQ
	cChave	:= xFilial("PQS") + ::cContrato

	PQS->(DbSeek(cChave))
	While ! PQS->(EOF()) .And. cChave == PQS->PQS_FILIAL + PQS->PQS_CONTRA
		cAMIni := CmpToAM(PQS->PQS_COMPET)
		cSeq      := PQS->PQS_SEQ
		cLinRec   := PQS->PQS_LINREC
		nPerc     := PQS->PQS_PERC
		dDtLim    := PQS->PQS_DTLIM
		If PQS->(FieldPos("PQS_CORP")) > 0
			cAMFim    := CmpToAM(PQS->PQS_CMPFIM)
			cCorp     := PQS->PQS_CORP      // 1=Sim,2=Nao
			cFase     := PQS->PQS_FASE      // 1,2,3,4....
		EndIf
		cObs := PQS->PQS_OBS

		If PQS->PQS_STATUS <> "C"
			Aadd(aReajExtra, {cAMIni, cSeq, cLinRec, nPerc, dDtLim, cAMFim, cCorp, cFase, cObs, PQS->(Recno()) } )
		EndIf
		PQS->(DbSkip())
	End

	ASort(aReajExtra, , , {|x,y| x[1] + x[2] + StrZero(x[10], 15) < y[1] + y[2] + StrZero(y[10], 15)})

	RestArea(aAreaPQS)

Return


Method LoadPH5(aFinanceiro) CLASS TGCVXC10
	Local oModCNB  := ::oModelo:GetModel("CNBDETAIL")
	Local cNumero  := oModCNB:GetValue("CNB_NUMERO")
	Local cItem    := oModCNB:GetValue("CNB_ITEM")
	Local aArea     := GetArea()
	Local aAreaPH5  := PH5->(GetArea("PH5"))
	Local cTmpPH5   := GetNextAlias()
	Local cQryPH5   := ""

	Local cChavePH6 := ""
	Local cChavePH7 := ""
	Local nPAnoMes  := 0
	Local aLinPH5   := {}
	Local nx        := 0

	PH6->(DBsetOrder(1))
	PH7->(DBsetOrder(1))

	cQryPH5 := "SELECT PH5.R_E_C_N_O_ RECNO "
	cQryPH5 += "  FROM " + RetSqlName("PH5") + " PH5 "
	cQryPH5 += " WHERE PH5.PH5_FILIAL = '" + xFilial("PH5")  + "' "
	cQryPH5 += "   AND PH5.PH5_CONTRA = '" + ::cContrato     + "' "
	cQryPH5 += "   AND PH5.PH5_REVISA = '" + ::cRevAtuDb     + "' "
	cQryPH5 += "   AND PH5.PH5_NUMERO = '" + cNumero         + "' "
	cQryPH5 += "   AND PH5.PH5_ITEM   = '" + cItem           + "' "
	cQryPH5 += "   AND PH5.PH5_ANOMES BETWEEN '" + CmptoAm(::cCmpGvIni) + "' AND  '" +  CmptoAm(::cCmpFim) + "'"
	cQryPH5 += "   AND PH5.D_E_L_E_T_ = ' ' "

	DbUseArea(.T., "TOPCONN", TcGenQry(NIL, NIL, cQryPH5), cTmpPH5, .T., .F.)

	While ! (cTmpPH5)->(EOF())
		PH5->(DbGoto((cTmpPH5)->RECNO))
		cAnoMes := Right(PH5->PH5_COMPET, 4) + Left(PH5->PH5_COMPET, 2)

		cChavePH6 := xFilial("PH6") + PH5->(PH5_CONTRA + PH5_REVISA + PH5_NUMERO + PH5_COMPET + PH5_CONDIC + PH5_CLIENT + PH5_LOJA + PH5_CONDPG + PH5_NOTASE + PH5_MOEDA + PH5_MASCCC + PH5_GU + PH5_SEQ)
		PH6->(DbSeek(cChavePH6))

		cChavePH7 := xFilial("PH7") + PH5->(PH5_CONTRA + PH5_REVISA + PH5_NUMERO + PH5_COMPET + PH5_CONDIC + PH5_MOEDA + PH5_GU)
		PH7->(DbSeek(cChavePH7))

		aLinPH5 := {}
		For nx:= 1 to PH5->(Fcount())
			aadd(aLinPH5, {PH5->(FieldName(nx)), PH5->(FieldGet(nx))} )
		Next
		aadd(aLinPH5, {"PH6_STATUS", PH6->PH6_STATUS}   )
		aadd(aLinPH5, {"PH7_STATUS", PH7->PH7_STATUS}   )
		aadd(aLinPH5, {"PH5_RECNO" , (cTmpPH5)->RECNO}  )

		aadd(aFinanceiro, aClone(aLinPH5))

		If Empty(CmptoAM(::cCmpIni))  .or. cAnoMes < CmptoAM(::cCmpIni)
			::cCmpIni := AMtoCmp(cAnoMes)
		EndIf
		If cAnoMes > ::cAMFim
			::cCmpFim  := AMtoCmp(cAnoMes)
			::cAMFim   := cAnoMes
		EndIf

		(cTmpPH5)->(DbSkip())
	End

	If Select(cTmpPH5) > 0
		(cTmpPH5)->(DbCloseArea())
	EndIf

	If ! Empty(aFinanceiro)
		nPAnoMes := PH5->(FieldPos("PH5_ANOMES"))
		aSort(aFinanceiro,,, {|x,y| x[nPAnoMes, 2] < y[nPAnoMes, 2] })
	EndIf

	RestArea(aAreaPH5)
	RestArea(aArea)
Return



Method LoadPNG(aCliente, aRoyalties) CLASS TGCVXC10
	Local cChave    := ""
	Local cCliente := aCliente[1]
	Local aAreaPNG := PNG->(GetArea("PNG"))
	Local oModCNB  := ::oModelo:GetModel("CNBDETAIL")
	Local cStatRM  := oModCNB:GetValue("CNB_STATRM")
	Local cCdRoyRev := Alltrim(GetMV('TI_BMSTRRY',,"120"))
	Local cCdTefRev := Alltrim(GetMV('TI_BMSTTFC',,"130")) 	// TEF - Revenda - Recorrente Variavel


	aRoyalties := {}

	If ! Self:IsRoyalties(oModCNB)
		Return
	EndIf

	cChave := xFilial("PNG") + cCliente

	PNG->(DbSetOrder(1))
	PNG->(DbSeek(cChave))
	While PNG->(! Eof() .and. cChave == PNG_FILIAL + PNG_CLIENT)
		If PNG->PNG_STATUS == "3" // 3=logFinalizado
			PNG->(DbSkip())
			Loop
		EndIf

		If PNG->PNG_CINTEG == "000003" .And. cStatRM <> cCdRoyRev
			PNG->(DbSkip())
			Loop
		EndIf

		If PNG->PNG_CINTEG == "000014" .And. cStatRM <> cCdTefRev
			PNG->(DbSkip())
			Loop
		EndIf

		PNG->(aadd(aRoyalties, {CmpToAM(PNG_COMPET), PNG_LSTROY, PNG_DELTA, PNG_IDPROC, PNG->PNG_STATUS }))
		PNG->(DbSkip())
	End

	If Len(aRoyalties) > 0
		aSort(aRoyalties,,, {|x,y| x[1] < y[1] })
	EndIf
	RestArea(aAreaPNG)

Return

Method IsRoyalties() CLASS TGCVXC10
	Local oModCNA   := ::oModelo:GetModel("CNADETAIL")
	Local cTipPla   := oModCNA:GetValue("CNA_TIPPLA")
	Local oModCNB   := ::oModelo:GetModel("CNBDETAIL")
	Local cStatRM   := oModCNB:GetValue("CNB_STATRM")
	Local cSituac   := oModCNB:GetValue("CNB_SITUAC")
	Local cCdRoyRev := Alltrim(SuperGetMV('TI_BMSTRRY',,"120"))
	Local cLcRecRy	:= Left(Alltrim(SuperGetMV('TI_BMCNARY',,"12")), 2)
	Local cAgrpRec	:= Padr(Alltrim(SuperGetMv("TI_AGRLREC")), Len(CNL->CNL_CODAGR)) 	//Agrupador de linha de Receita
	Local aAreaCNL  := CNL->(GetArea())
	Local cCdTefRev := Alltrim(GetMV('TI_BMSTTFC',,"130")) 	// TEF - Revenda - Recorrente Variavel

	If cStatRM == cCdTefRev
		Return .T.
	EndIf

	If ! cStatRM == cCdRoyRev
		Return .F.
	EndIf

	If ! cSituac == "A"
		Return .F.
	EndIf

	If ! CNL->(CNL_FILIAL+CNL_CODAGR+CNL_NIVAGR) == xFilial("CNL") + cAgrpRec + cLcRecRy
		CNL->(dbSetOrder(2)) // CNL_FILIAL+CNL_CODAGR+CNL_NIVAGR
		If ! CNL->(dbSeek(xFilial("CNL") + cAgrpRec + cLcRecRy))
			RestArea(aAreaCNL)
			Return .F.
		EndIf
		RestArea(aAreaCNL)
	EndIf

	If CNL->CNL_CODIGO == cTipPla // royalties
		Return .T.
	EndIf

Return .F.

Method LoadCroFin( aCarenVnd, dLimRevisa) CLASS TGCVXC10
	Local oModCNB     := ::oModelo:GetModel("CNBDETAIL")
	Local dDtFim      := CtoD("")
	Local cDescMon    := GetDescMon()
	Local nFatorImp   := 0

	Default dLimRevisa := NIL


	::cPlanilha   := oModCNB:GetValue("CNB_NUMERO")
	::cItem       := oModCNB:GetValue("CNB_ITEM"  )
	::cProduto    := oModCNB:GetValue("CNB_PRODUT")
	::cDescPro    := SB1->B1_DESC
	::cTipRec     := oModCNB:GetValue("CNB_TIPREC")
	::cTipCnt     := oModCNB:GetValue("CNB_XTPCNT")
	::dVencto     := oModCNB:GetValue("CNB_VENCTO")

	::cFreFat	  := oModCNB:GetValue("CNB_XFREFA")
	::cTpPago	  := oModCNB:GetValue("CNB_XTPPG")
	::nDiafat	  := oModCNB:GetValue("CNB_XDIAFA")

	::nCarenVnd   := aCarenVnd[1]
	::cCarIniVnd  := aCarenVnd[2]
	::cCarFinVnd  := aCarenVnd[3]

	::cCondPG     := oModCNB:GetValue("CNB_CONDPG")
	::cCodISS     := SB1->B1_CODISS
	::cNotaSe     := oModCNB:GetValue("CNB_NOTASE")
	::cMoeda      := oModCNB:GetValue("CNB_MOEDA")
	::cMascCC     := oModCNB:GetValue("CNB_MASCCC")
	::cStatRM     := oModCNB:GetValue("CNB_STATRM")
	::cGrpEmp     := oModCNB:GetValue("CNB_GRUPO")
	::cUniNeg     := oModCNB:GetValue("CNB_UNINEG")
	::cPropos     := oModCNB:GetValue("CNB_PROPOS")
	::cRevPro     := oModCNB:GetValue("CNB_PROREV")
	::cItmPro     := oModCNB:GetValue("CNB_PROITN")
	::cFoldPr     := oModCNB:GetValue("CNB_XFLDPR")
	::dDtIni      := CtoD("")
	::dConIni     := ctod("")
	::dConFin     := ctod("")
	::cAnoMesI    := ""
	::cAnoMesF    := ""
	::nQtdMes     := 0
	::cAMAtu      := Left(Dtos(Date()),6)
	::nQuant      := Self:RetQtdOri()
	::nQtdRev     := oModCNB:GetValue("CNB_QUANT")
	::nVlrUniCtr  := U_RoundUnid(oModCNB:GetValue("CNB_VLUNIT"),,oModCNB:GetValue("CNB_UNINEG"))
	::nPeriodico  := Val(oModCNB:GetValue("CNB_CONDIC")) + 1
	::nImpostC    := 0
	::cModImpC    := oModCNB:GetValue("CNB_IMPOST")
	::cCmpFIni    := oModCNB:GetValue("CNB_XCOMPE")
	::cLinRec     := SBM->BM_XLINREC
	::lAudita     := .F.
	::cSituac     := oModCNB:GetValue("CNB_SITUAC")
	::lBilling    := oModCNB:GetValue("CNB_XBILLI") == '1' .and. oModCNB:GetValue("CNB_XTPCNT")  <> '1'
	::lRoyalties  := Self:IsRoyalties()
	::lCorpora    := Left( AllTrim(SBM->BM_GRUREL), 11 ) == "CORPORATIVO"

	// Inicio
	::dDtIni     := oModCNB:GetValue("CNB_DTSITU")
	::dConIni    := ::dDtIni


	If Empty(::dDtIni) .or. ::dDtIni < ctod('01/08/16')
		::dDtIni := ctod('01/08/16')
	EndIf

	If ::nPeriodico == 1  // exclusivo para mensal
		::cCmpFIni := AmtoCMP(Left(dtos(::dDtIni),6))
		If ! Empty(CmptoAM(::cCarIniVnd)) .and. CmptoAM(::cCarIniVnd) < CmpToAm(::cCmpFIni)
			::cCmpFIni := ::cCarIniVnd
		EndIf
		If ! Empty(CmptoAM(::cCmpIni)) .and. CmptoAM(::cCmpIni) < CmpToAm(::cCmpFIni)
			::cCmpFIni := ::cCmpIni
		EndIf
	EndIf


	::cAnoMesI    := Strzero(Year(::dDtIni), 4) + StrZero(Month(::dDtIni), 2)          // AAAAMM

	dDtFim      := oModCNB:GetValue("CNB_VIGFIM")

	If ::dDtIni > dDtFim
		::dDtIni := dDtFim
	EndIf

	::dConFin     := dDtFim
	::cAnoMesF    := Strzero(Year(dDtFim), 4) + StrZero(Month(dDtFim), 2)          // AAAAMM

	If dLimRevisa <> NIL .and. Dtos(dDtFim) > Dtos(dLimRevisa)
		dDtFim   := dLimRevisa
	EndIf

	If Empty(dDtFim) .or. Dtos(dDtFim) >=  Dtos(MONTHSUM(Date() ,1))
		If Empty(::cAMFim)
			dDtFim   := MONTHSUM(Date(), 1)
		Else
			dDtFim   := Stod(::cAMFim + "01")
		EndIf
	EndIf

	dDtFim := ctod(dtoc(dDtFim))

	::nQtdMes     := AMCalcDif(Left(Dtos(::dDtIni), 6), Left(Dtos(dDtFim), 6)) + 1

	nFatorImp    := U_TFATA012(::cProduto, ::cModImpC, ::cGrpEmp, ::cUniNeg, ::cCondPG)
	U_GVFUNTPM(::cCondPG, @nFatorImp)
	::nImpostC := nFatorImp

	FWMonitorMsg( cDescMon)

	If Left(::cLinRec, 2) == "03"   //SMS
		PHV->(DbSetOrder(1))  //PHV_FILIAL+PHV_CONTRA+PHV_REVISA+PHV_GRUPO+PHV_UNINEG
		PHV->(DbSeek(xFilial("PHV") + ::cContrato + ::cRevisa + ::cGrpEmp + ::cUniNeg ))
		If ! PHV->(Eof()) .and. PHV->PHV_AUDITA == "1"
			::lAudita := .T.
		EndIf
	Endif

Return

Method CalcCroFin(aCliente, nPosCli, aSituac, aCancela, aCarencia, aBonifica, aReajuste, aAjustFin, aP68Intera, aTransf, aFinanceiro, aCtrlPer, aBilling, aRoyalties, aTroca, aReajExtra, aExcecao, aHistoric, aPHG, aCNCMain) CLASS TGCVXC10

	Local nVlrTotCtr := ::nQuant * ::nVlrUniCtr
	Local cCliente   := aCliente[nPosCli, 1]
	Local cLoja      := aCliente[nPosCli, 2]
	Local cDescCli   := aCliente[nPosCli, 3]
	Local lSetorPub  := Posicione("AI0", 1, xFilial("AI0") + cCliente + cLoja , "AI0->AI0_SETPUB") == "1"

	Local cGU        := ::cGrpEmp + ::cUniNeg
	Local oCrono     := NIL
	Local cDescMon   := GetDescMon()
	Local nx
	Local np
	Local ny


	FWMonitorMsg( cDescMon)

	// Cria o objeto
	oCrono:= Tgcvxc05():New()
	oCrono:SetContrato(::cContrato, ::cRevisa, ::cPlanilha, ::cItem, ::cProduto, ::cDescPro, ::cTipRec, ::cTipCnt, ::dVencto)
	oCrono:SetCliente(cCliente, cLoja, cDescCli, lSetorPub)
	oCrono:SetCarenVnd(::nCarenVnd, ::cCarIniVnd, ::cCarFinVnd)
	oCrono:SetCNB(::cCondPG, ::cCodISS, ::cNotaSe, ::cMoeda, ::cMascCC, ::cStatRM, ::cGrpEmp, ::cUniNeg, cGU, ::cFreFat, ::cTpPago, ::nDiafat)
	oCrono:SetProposta(::cPropos, ::cRevPro, ::cItmPro, ::cFoldPr)

	// variveis para calculo do calentario
	oCrono:dDtIni     := ::dDtIni
	oCrono:dConIni    := ::dConIni
	oCrono:dConFin    := ::dConFin
	oCrono:cAnoMesI   := ::cAnoMesI
	oCrono:cAnoMesF   := ::cAnoMesF
	oCrono:nQtdMes    := ::nQtdMes

	// Quantidade e Valores da CNB
	oCrono:cAMAtu     := ::cAMAtu
	oCrono:nQuant     := ::nQuant
	oCrono:nQtdRev    := ::nQtdRev
	oCrono:nVlrUniCtr := ::nVlrUniCtr
	oCrono:nVlrTotCtr := nVlrTotCtr

	oCrono:nPeriodico := ::nPeriodico

	oCrono:nImpostC   := ::nImpostC
	oCrono:cModImpC   := ::cModImpC
	oCrono:cCmpFIni   := ::cCmpFIni
	oCrono:cLinRec    := ::cLinRec

	If ::lAudita
		oCrono:nPercAudi  := 2.0442
	EndIf

	oCrono:aSituac    := aClone(aSituac    )
	oCrono:cSituac    := ::cSituac
	oCrono:aCliente   := aClone(aCliente   )
	oCrono:aCancela   := aClone(aCancela   )
	oCrono:aCarencia  := aClone(aCarencia  )
	oCrono:aBonifica  := aClone(aBonifica  )
	oCrono:aReajuste  := aClone(aReajuste  )
	oCrono:aAjustFin  := aClone(aAjustFin  )
	oCrono:aP68Intera := aClone(aP68Intera )
	oCrono:aFinanceiro:= aClone(aFinanceiro)
	oCrono:aTransf    := aClone(aTransf    )
	oCrono:aCtrlPer   := aClone(aCtrlPer   )
	oCrono:aBilling   := aClone(aBilling   )
	oCrono:lBilling   := ::lBilling
	oCrono:lRoyalties := ::lRoyalties
	oCrono:aRoyalties := aClone(aRoyalties)
	oCrono:lCorpora   := ::lCorpora
	oCrono:aTroca     := aClone(aTroca    )
	oCrono:aReajExtra := aClone(aReajExtra)
	oCrono:aExcecao   := aClone(aExcecao  )
	oCrono:aHistoric  := aClone(aHistoric )
	oCrono:aPHG       := aClone(aPHG      )
	oCrono:aCNCMain   := aClone(aCNCMain  )
	oCrono:Cria()
	oCrono:Calc()

	aadd(::aCroItem, {::cPlanilha, ::cItem, cCliente, cLoja, oCrono})  // utilizando na consulta por item na simulação

	For nx:= 1 to len(oCrono:aAMAtu)
		If oCrono:aAMAtu[nx] < ::cAMGvIni
			Loop
		EndIf

		If FWIsInCallStack("U_TGCVA032") .and.  oCrono:aAMAtu[nx] < Left(Dtos(Date()), 6) .and. ! IsInCallStack("_sGeraCont")
			Loop
		EndIf

		np:= Ascan(::aAMAtu, oCrono:aAMAtu[nx])
		If Empty(nP)
			aadd(::aAMAtu, oCrono:aAMAtu[nx])
		EndIf
	Next

	For ny := 1 to len(::aAMAtu)
		np := Ascan(oCrono:aMsgErro, { |x| x[1] == ::aAMAtu[ny] })
		If ! Empty(nP)
			::cMsgErro += "Contrato:" + ::cContrato + CRLF
			::cMsgErro += "Planilha:" + ::cPlanilha + CRLF
			::cMsgErro += "Item:" + ::cItem + CRLF
			::cMsgErro += "Compentencia:" + AMtoCmp(oCrono:aMsgErro[np, 1]) + CRLF
			::cMsgErro += oCrono:aMsgErro[np, 2]
		EndIf
	Next
	//oCrono:FreeChild()
	//FreeObj(oCrono)
	//oCrono:=NIL

Return

Method RetQtdOri() CLASS TGCVXC10
	Local oModCNB   := ::oModelo:GetModel("CNBDETAIL")
	Local cPlanilha := oModCNB:GetValue("CNB_NUMERO")
	Local cItem     := oModCNB:GetValue("CNB_ITEM")
	Local cProduto  := oModCNB:GetValue("CNB_PRODUT")
	Local nQtdOri   := 0
	Local aAreaCNB  := CNB->(GetArea())
	Local aAreaCN9  := CN9->(GetArea())

	CN9->(DbSetOrder(1))
	CNB->(DbSetOrder(3))  // CNB_FILIAL+CNB_CONTRA+CNB_NUMERO+CNB_ITEM + CNB_REVISA
	cChave := xFilial("CNB") + ::cContrato + cPlanilha + cItem
	CNB->(DbSeek(cChave))

	While CNB->(! Eof() .and. CNB_FILIAL+CNB_CONTRA+CNB_NUMERO+CNB_ITEM == xFilial("CNB") + ::cContrato + cPlanilha + cItem )

		If CNB->CNB_PRODUT <> cProduto
			CNB->(DbSkip())
			Loop
		EndIf
		nQtdOri   := CNB->CNB_QUANT
		CNB->(DbSkip())

		If CNB->(Eof() .or. ! CNB_FILIAL+CNB_CONTRA+CNB_NUMERO+CNB_ITEM == xFilial("CNB") + ::cContrato + cPlanilha + cItem )
			Exit
		EndIf

		CN9->(DbSeek(xFilial("CN9") + CNB->CNB_CONTRA + CNB->CNB_REVISA))
		If Empty(CN9->(CN9_DTREV))
			CNB->(DbSkip())
			Loop
		EndIF
		Exit
	End


	RestArea(aAreaCNB)
	RestArea(aAreaCN9)

Return nQtdOri

Method Refresh() CLASS TGCVXC10
	Local aAMAtu    := ::aAMAtu
	Local cAMProc   := ""
	Local oItemCmp  := NIL
	Local nx        := 0
	Local ny        := 0
	Local lReIndex  := GetMv("TI_PERFCD", ,.F.)

	// etapa 1- sincronizar as arrays espelhos ph5, ph6, ph7
	Self:DelPH5Old()  // APAGA aPH5 FRACO
	Self:IndexPH5()
	For nx := 1 to len(aAMAtu)
		cAMProc := aAMAtu[nx]
		For ny := 1 to len(::aCroItem)
			oItemCmp:= ::aCroItem[ny, 5]:RetObjCmp(cAMProc)
			If oItemCmp == NiL
				Loop  // não encontrou o objeto com o mes referente
			EndIf
			oItemCmp:SalvaCtr(Self, lReIndex)
		Next
	Next

	If !lReIndex 
		Self:IndexPH5()
	Endif

	Self:Totaliza()
	If ::oModelo:GetModel("PH5DETAIL") <> NIL 
		// etapa 2 - sincronizar com o modelo
		Self:DelModOld() //APAGA ModPH5, ModPH6, ModPH7 FRACO
		Self:ModRefresh()
	EndIf 

Return

Method DelPH5Old() CLASS TGCVXC10
	Local nx        := 1
	Local aAMAtu    := ::aAMAtu
	Local aCroItem  := ::aCroItem
	Local cListAM   := ""
	Local cPlanilha := ""
	Local cItem     := ""
	Local cCliente  := ""
	Local cLoja     := ""
	Local np        := 0
	Local oCrono    := NIL
	Local oCroItem  := NIL
	Local cAMProc   := ""

	For nx:= 1 to len(aAMAtu)
		cListAM += aAMAtu[nx] + ","
	Next

	nx := 1
	While nx <= len(::aPH5)
		If Self:GetPH5("PH5_ANOMES", nx) $ cListAM .AND. Empty(Self:GetPH5("PH5_NUMMED", nx))
			cPlanilha :=  Self:GetPH5("PH5_NUMERO", nx)
			cItem     :=  Self:GetPH5("PH5_ITEM"  , nx)
			cCliente  :=  Self:GetPH5("PH5_CLIENT", nx)
			cLoja     :=  Self:GetPH5("PH5_LOJA"  , nx)
			cAMProc   :=  Self:GetPH5("PH5_ANOMES", nx)

			//np := Ascan(aCroItem, {|x| x[1] + x[2] + x[3] + x[4] == cPlanilha + cItem + cCliente + cLoja } )
			np := Ascan(aCroItem, {|x| x[1] + x[2]  == cPlanilha + cItem  } )
			If Empty(np)
				nx++
				Loop
			EndIf
			np := Ascan(aCroItem, {|x| x[1] + x[2] + x[3] + x[4] == cPlanilha + cItem + cCliente + cLoja } )
			If !  Empty(np)
				oCrono := aCroItem[np, 5]
				oCroItem := oCrono:RetObjCmp(cAMProc)
				If oCroItem == NIL
					nx++
					Loop
				EndIf
				If ! (oCroItem:cAcao == "Atualizar Cronograma" .or. oCroItem:cAcao == "Gerar Cronograma" .or. oCroItem:cAcao == "Excluir Cronograma")
					nx++
					Loop
				EndIf
			EndIf
			aDel(::aPH5, nx)
			aSize(::aPH5, len(::aPH5) - 1)
		Else
			nx++
		EndIf
	End

Return

Method IndexPH5() CLASS TGCVXC10

	Local nP1 := Self:PosPh5("PH5_CONTRA")
	Local nP2 := Self:PosPh5("PH5_REVISA")
	Local nP3 := Self:PosPh5("PH5_NUMERO")
	Local nP4 := Self:PosPh5("PH5_ANOMES")
	Local nP5 := Self:PosPh5("PH5_CONDIC")
	Local nP6 := Self:PosPh5("PH5_CLIENT")
	Local nP7 := Self:PosPh5("PH5_LOJA")
	Local nP8 := Self:PosPh5("PH5_CONDPG")
	Local nP9 := Self:PosPh5("PH5_NOTASE")
	Local nP10 := Self:PosPh5("PH5_MOEDA")
	Local nP11 := Self:PosPh5("PH5_MASCCC")
	Local nP12 := Self:PosPh5("PH5_GU")
	Local nP13 := Self:PosPh5("PH5_SEQ")


	//PH5_FILIAL+PH5_CONTRA+PH5_REVISA+PH5_NUMERO+PH5_COMPET+PH5_CONDIC+PH5_CLIENT+PH5_LOJA+PH5_CONDPG+PH5_NOTASE+PH5_MOEDA+PH5_MASCCC+PH5_GU+PH5_SEQ
	aSort(::aPH5,,, {|x, y|  x[nP1] + x[nP2] + x[nP3] + x[nP4] + x[nP5] + x[nP6] + x[nP7] + x[nP8] + x[nP9] + x[nP10] + x[nP11] + x[nP12]  + x[nP13] < ;
		y[nP1] + y[nP2] + y[nP3] + y[nP4] + y[nP5] + y[nP6] + y[nP7] + y[nP8] + y[nP9] + y[nP10] + y[nP11] + y[nP12]  + y[nP13] })

Return

Method DelModOld() CLASS TGCVXC10
	Local nx        := 1
	Local aAMAtu    := ::aAMAtu
	Local cListAM   := ""
	Local oModPH5 := ::oModelo:GetModel("PH5DETAIL")
	Local oModPH6 := ::oModelo:GetModel("PH6DETAIL")
	Local oModPH7 := ::oModelo:GetModel("PH7DETAIL")
	Local nPH5    := 0
	Local nPH6    := 0
	Local nPH7    := 0
	Local lDelPH7 := .T.
	Local cCompet := ""

	If oModPH5 == NIL 
		Return 
	EndIf 

	For nx:= 1 to len(aAMAtu)
		cListAM += aAMAtu[nx] + ","
	Next
	U_GV002MCR(::oModelo, .T.)

	For nPH7 := oModPH7:Length() to  1 Step -1
		oModPH7:Goline(nPH7)
		If oModPH7:IsDeleted()
			Loop
		EndIf
		cCompet := oModPH7:GetValue('PH7_COMPET')
		If ! CmptoAM(cCompet) $ cListAM
			Loop
		EndIf
		lDelPH7 := .T.
		For nPH6 := oModPH6:Length() to  1 Step -1 // Exclui os Cabeçalhos
			oModPH6:Goline(nPH6)
			If oModPH6:IsDeleted()
				Loop
			EndIf

			If ! Empty(oModPH6:GetValue('PH6_NUMMED'))
				lDelPH7 := .F.
				Loop
			EndIf

			For nPH5 := oModPH5:Length() to  1 Step -1 //Exclui os Itens

				oModPH5:Goline(nPH5)
				If ! Empty(oModPH5:GetValue('PH5_NUMMED'))
					lDelPH7 := .F.
					Loop
				EndIf

				oModPH5:DeleteLine()
			Next
			If lDelPH7
				oModPH6:DeleteLine()
			EndIf
		Next
		If lDelPH7
			oModPH7:DeleteLine()
		EndIf
	Next
	U_GV002MCR(::oModelo, .F.)

Return

Method ModRefresh() CLASS TGCVXC10
	Local oModPH5  := ::oModelo:GetModel("PH5DETAIL")
	Local oModPH6  := ::oModelo:GetModel("PH6DETAIL")
	Local oModPH7  := ::oModelo:GetModel("PH7DETAIL")
	Local aAreaSM0 := SM0->(GetArea())
	Local nPosPH5  := 0
	Local nPosPH6  := 0
	Local nPosPH7  := 0

	Local aPH5Seek := {}
	Local aPH6Seek := {}
	Local aPH7Seek := {}
	Local cChave   := ""
	Local cCampo   := ""
	Local cListAM  := ""
	Local aAMAtu   := ::aAMAtu
	Local nPAMes   := Self:PosPH5("PH5_ANOMES")
	Local nx       := 0

	For nx:= 1 to len(aAMAtu)
		cListAM += aAMAtu[nx] + ","
	Next

	aSort(::aPH5,,, {|x, y|  x[nPAMes] < y[nPAMes]})


	Self:ProcMensal()


	U_GV002MCR(::oModelo, .T.)
	For nPosPH5:= 1 to Len(::aPH5)

		If ! CmpToAM(Self:GetPH5("PH5_COMPET", nPosPH5)) $ cListAM
			Loop
		EndIf
		If ! IsBlind()
			IncProc("Sincronizando Modelo - Competencia: " +  Self:GetPH5("PH5_COMPET", nPosPH5))
			ProcessMessage()
			If lAbortPrint
				Return
			EndIf
		EndIf

		// Verifica se tem PH7
		aPH7Seek := {   {"PH7_NUMERO",	Self:GetPH5("PH5_NUMERO", nPosPH5) },;
			{"PH7_COMPET",	Self:GetPH5("PH5_COMPET", nPosPH5) },;
			{"PH7_CONDIC",	Self:GetPH5("PH5_CONDIC", nPosPH5) },;
			{"PH7_MOEDA",	Self:GetPH5("PH5_MOEDA" , nPosPH5) },;
			{"PH7_GRUPO",	Self:GetPH5("PH5_GRUPO" , nPosPH5) },;
			{"PH7_UNINEG",	Self:GetPH5("PH5_UNINEG", nPosPH5) }}
		If ! oModPH7:SeekLine(aPH7Seek)
			If ! Empty(oModPH7:GetValue("PH7_NUMERO"))
				If oModPH7:Length() == oModPH7:AddLine()
					::cMsgErro := "Não foi possivel criar linha na PH7!" + CRLF + U_GVGStrEr(::oModelo)
					Return
				EndIf
			EndIf
			oModPH7:Goline(oModPH7:GetLine())

			cChave := Self:GetPH5("PH5_CONTRA", nPosPH5)
			cChave += Self:GetPH5("PH5_REVISA", nPosPH5)
			cChave += Self:GetPH5("PH5_NUMERO", nPosPH5)
			cChave += Self:GetPH5("PH5_COMPET", nPosPH5)
			cChave += Self:GetPH5("PH5_CONDIC", nPosPH5)
			cChave += Self:GetPH5("PH5_MOEDA" , nPosPH5)
			cChave += Self:GetPH5("PH5_GU"    , nPosPH5)

			nPosPH7 := Self:FindPH7(cChave)
			For nx:= 1 to len(::aEstruPH7)
				cCampo := ::aEstruPH7[nx, 2]
				oModPH7:LoadValue(cCampo, Self:GetPH7(cCampo, nPosPH7))
			Next
		Else
			oModPH7:Goline(oModPH7:GetLine())
		EndIf

		// VERIFICA SE TEM PH6
		aPH6Seek := {   {"PH6_NUMERO",	Self:GetPH5("PH5_NUMERO", nPosPH5) },;
			{"PH6_COMPET",	Self:GetPH5("PH5_COMPET", nPosPH5) },;
			{"PH6_CONDIC",	Self:GetPH5("PH5_CONDIC", nPosPH5) },;
			{"PH6_CLIENT",	Self:GetPH5("PH5_CLIENT", nPosPH5) },;
			{"PH6_LOJA"  ,	Self:GetPH5("PH5_LOJA"  , nPosPH5) },;
			{"PH6_CONDPG",	Self:GetPH5("PH5_CONDPG", nPosPH5) },;
			{"PH6_NOTASE",	Self:GetPH5("PH5_NOTASE", nPosPH5) },;
			{"PH6_MOEDA",	Self:GetPH5("PH5_MOEDA" , nPosPH5) },;
			{"PH6_MASCCC",	Self:GetPH5("PH5_MASCCC", nPosPH5) },;
			{"PH6_GU"    ,	Self:GetPH5("PH5_GU"    , nPosPH5) },;
			{"PH6_SEQ"   ,	Self:GetPH5("PH5_SEQ"   , nPosPH5) }}

		If ! oModPH6:SeekLine(aPH6Seek)
			If ! Empty(oModPH6:GetValue("PH6_NUMERO"))  // se tive a linha em branco reaproveita
				If oModPH6:Length() == oModPH6:AddLine()
					::cMsgErro := "Não foi possivel criar linha na PH6!" + CRLF + U_GVGStrEr(::oModelo)
					Return
				EndIf
			EndIf
			oModPH6:Goline(oModPH6:GetLine())

			// atualiza PH6
			cChave := Self:GetPH5("PH5_CONTRA", nPosPH5)
			cChave += Self:GetPH5("PH5_REVISA", nPosPH5)
			cChave += Self:GetPH5("PH5_NUMERO", nPosPH5)
			cChave += Self:GetPH5("PH5_COMPET", nPosPH5)
			cChave += Self:GetPH5("PH5_CONDIC", nPosPH5)
			cChave += Self:GetPH5("PH5_CLIENT", nPosPH5)
			cChave += Self:GetPH5("PH5_LOJA"  , nPosPH5)
			cChave += Self:GetPH5("PH5_CONDPG", nPosPH5)
			cChave += Self:GetPH5("PH5_NOTASE", nPosPH5)
			cChave += Self:GetPH5("PH5_MOEDA" , nPosPH5)
			cChave += Self:GetPH5("PH5_MASCCC", nPosPH5)
			cChave += Self:GetPH5("PH5_GU"    , nPosPH5)
			cChave += Self:GetPH5("PH5_SEQ"   , nPosPH5)

			nPosPH6 := Self:FindPH6(cChave)
			For nx:= 1 to len(::aEstruPH6)
				cCampo := ::aEstruPH6[nx, 2]
				oModPH6:LoadValue(cCampo, Self:GetPH6(cCampo, nPosPH6))
			Next
		Else
			oModPH6:Goline(oModPH6:GetLine())
		EndIf

		aPH5Seek:= {{"PH5_NUMERO",  Self:GetPH5("PH5_NUMERO", nPosPH5) },;
			{"PH5_COMPET",  Self:GetPH5("PH5_COMPET", nPosPH5) },;
			{"PH5_CONDIC",  Self:GetPH5("PH5_CONDIC", nPosPH5) },;
			{"PH5_CLIENT",  Self:GetPH5("PH5_CLIENT", nPosPH5) },;
			{"PH5_LOJA"  ,  Self:GetPH5("PH5_LOJA"  , nPosPH5) },;
			{"PH5_CONDPG",  Self:GetPH5("PH5_CONDPG", nPosPH5) },;
			{"PH5_NOTASE",  Self:GetPH5("PH5_NOTASE", nPosPH5) },;
			{"PH5_MOEDA" ,  Self:GetPH5("PH5_MOEDA" , nPosPH5) },;
			{"PH5_MASCCC",  Self:GetPH5("PH5_MASCCC", nPosPH5) },;
			{"PH5_ITEM"  ,  Self:GetPH5("PH5_ITEM"  , nPosPH5) },;
			{"PH5_PRODUT",  Self:GetPH5("PH5_PRODUT", nPosPH5) },;
			{"PH5_GRUPO" ,  Self:GetPH5("PH5_GRUPO" , nPosPH5) },;
			{"PH5_UNINEG",  Self:GetPH5("PH5_UNINEG", nPosPH5) },;
			{"PH5_SEQ"   ,  Self:GetPH5("PH5_SEQ"   , nPosPH5) }}

		If ! oModPH5:SeekLine(aPH5Seek)
			If ! Empty(oModPH5:GetValue("PH5_NUMERO"))  // se tive a linha em branco reaproveita
				If oModPH5:Length() == oModPH5:AddLine()
					::cMsgErro := "Não foi possivel criar linha na PH5!" + CRLF + U_GVGStrEr(::oModelo)
					Return
				EndIf
			EndIf
		EndIf
		oModPH5:Goline(oModPH5:GetLine())

		For nx:= 1 to len(::aEstruPH5)
			cCampo := ::aEstruPH5[nx, 2]
			oModPH5:LoadValue(cCampo, Self:GetPH5(cCampo, nPosPH5))
		Next
	Next




	U_GV002MCR(::oModelo, .F.)
	RestArea(aAreaSM0)
Return

Method ProcMensal() CLASS TGCVXC10
	Local ni      := 0
	Local cPropos := ""
	Local cIniFat := ""
	Local aItensM := {}
	Local oMensal := Nil


	For ni:=1 to Len(::aMensal)
		cPropos := ::aMensal[ni, 1]
		cIniFat := ::aMensal[ni, 2]
		aItensM := aClone(::aMensal[ni, 3])

		AtuaPWF(cPropos, cIniFat)

		oMensal := Tgcvxc32():New(::cContrato, ::cRevisa, cPropos, aItensM, aClone(::aAMAtu))
		oMensal:Load()
		oMensal:ProcModMen(Self)
		oMensal:Destroy()
		FreeObj(oMensal)

		aSize(aItensM, 0)

	Next
Return

Static Function AtuaPWF(cPropos, cIniFat)

	PWF->(DbSetOrder(2)) //PWF_FILIAL+PWF_PROPMS

	If PWF->(DbSeek(xFilial("PWF") + cPropos))

		If Empty(PWF->PWF_INIMEN)

			If PWF->(RecLock("PWF", .F.))
				PWF->PWF_INIMEN := cIniFat
				PWF->PWF_ENDMEN := SomaAM(PWF->PWF_INIMEN , PWF->PWF_REPASS - 1)
				PWF->(MSUnLock())
			EndIf
		EndIf
	EndIf

Return



//Tratamento de datas e competencias
Static Function AMtoCmp(cAM)
	Local cCmp := Right(cAM, 2) + "/" + Left(cAM, 4)

Return cCmp

Static Function CmptoAM(cCmp)
	Local cAM := Right(cCmp, 4) + Left(cCmp, 2)

Return cAM

Static Function AMAdicMes(cAnoMes, nMes)
	Local cMes := ""
	Local cAno := ""
	Local cMAAux := cAnoMes
	Local nx

	For nx := 1 to nMes
		cMes:= Right(cMAAux, 2)
		cAno:= Left(cMAAux , 4)
		If cMes == "12"
			cMAAux := Soma1(cAno)+"01"
		Else
			cMAAux := cAno + Soma1(cMes)
		EndIf
	Next

Return cMAAux

Static Function AMCalcDif(cAMMenor, cAMMaior)
	Local nQtdMes:= 0
	Local cAno   := ""
	Local cMes   := ""
	Local cCmp   := ""

	If "/" $ cAMMenor  // se tiver barra está no formato MM/AAAA
		cAMMenor := Right(cAMMenor,4) + Left(cAMMenor,2)
	EndIf
	If "/" $ cAMMaior
		cAMMaior := Right(cAMMaior,4) + Left(cAMMaior,2)
	EndIf

	If Empty(cAMMenor)
		Return nQtdMes
	EndIf

	cCmp := cAMMenor
	While cCmp < cAMMaior
		cAno := Left(cCmp, 4)
		cMes := Right(cCmp, 2)
		If cMes == "12"
			cMes := "01"
			cAno := Soma1(cAno)
		Else
			cMes := Soma1(cMes)
		EndiF
		cCmp := cAno + cMes
		nQtdMes++
	End

Return nQtdMes

Static Function GetDescMon()
	Local cDescMon := ""
	Local aInfo    := GetUserInfoArray()
	Local np       := 0

	np := ascan(aInfo, {|x| x[3] == ThreadId()})

	If np > 0
		cDescMon  := aInfo[np, 11]
	EndIf

Return cDescMon

Static Function TiraAM(cAm, nMes)
	Local cMes := ""
	Local cAno := ""
	Local ni   := 0

	For ni:= 1 to nMes
		cMes := Right(cAM, 2)
		cAno := Left(cAM, 4)

		If cMes == "01"
			cAM := Tira1(cAno) + "12"
		Else
			cAM := Tira1(cAM)
		EndIf
	Next
Return cAM

Static Function IsMensal(cPropos, cItmProp, cProduto)
Return U_GCVXC32A(cPropos, cItmProp, cProduto)

