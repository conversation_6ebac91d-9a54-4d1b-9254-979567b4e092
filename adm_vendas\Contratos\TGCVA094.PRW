#include "totvs.ch"

/*
U_GCVA094V(xContrato, xPlanilha, xItem)    // Mostra em tela o calculo do cronograma financeiro
U_GCVA094A(cContrato, cPlanx, cItemx, aAMSave, lGeraHtml, cHtml, cArqCSV, cMsgErro, cLimRevisa, dLimRevisa, lDetPh5, lSelSave)  //Função para calcular o calculo do cronograma financeiro com o uso da classe
*/


/*{Protheus.doc} GCVA094V
Função para mostrar tela com o calculo do cronograma financeiro 

@param xContrato
@param xPlanilha
@param xItem


<AUTHOR>
@since 01/10/2018
@return Nil

*/

User Function GCVA094V(xContrato, xPlanilha, xItem)
    Local oDlg
    Local oRect
    Local oSize
    Private lSimula := .F.
    Private lDetPh5 := .F.
    
    oSize := FwDefSize():New(.F.)
	oSize:AddObject( "PANEL" , 100, 100, .T., .T. )
	oSize:aWorkArea := {0, 25, oMainWnd:nRight - 15, oMainWnd:nBottom - 37}
	oSize:lProp := .T.
	oSize:Process()

    
    oRect := TRect():New(0, 0, oMainWnd:nBottom - 37, oMainWnd:nRight - 12)	

	DEFINE MSDIALOG oDlg FROM 0,0 TO 0,0 TITLE  "Contrato - Cronograma Financeiro"

        oDlg:SetCoors(oRect)
   
        Tela1(oDlg, oSize, xContrato, xPlanilha, xItem)
   
	ACTIVATE MSDIALOG oDlg 
    
Return


Static Function Tela1(oDlg, oSize, xContrato, xPlanilha, xItem )
    Local nB := oSize:GetDimension("PANEL", "LINEND")
	Local nR := oSize:GetDimension("PANEL", "COLEND")
    Local oBut
    Local oBut2
    Local oPnlBUT
    Local oPnlSup
    Local aoScrSup := Array(11)
    Local aoEdit   := Array(11)
    Local oContrato
    Local cContrato := Space(15)
    Local oPlanilha
    Local cPlanilha := Space(06)
    Local oItem
    Local cItem     := Space(06)
    Local cCmpFim   := AMtoCmp(Left(Dtos(Date()), 6))
    Local oLimRevisa
    Local cLimRevisa:= Space(06)
    Local aLimRevisa:= {}
    Local aDtRevisa := {}
    Local dLimRevisa:= Date() 
    Local oCmpFim
    Local oCheck
    Local aFolder  := {"Cronograma", "Situação", "Cancelamento", "Carência", "Bonificação","Reajuste","Transferência", "Periodo", "Billing","Troca", "Reajuste Extra."}
    Local oFol 
    Local nx

    If xContrato <> NIL
        cContrato := xContrato        
    EndIf 
    If xPlanilha <> NIL
       cPlanilha :=  xPlanilha
    End
    If xItem <> NIL
        cItem := xItem
    EndIf

    oPnlBUT := TPanelCss():New(,,,oDlg)
    oPnlBUT :SetCoors(TRect():New( 0,0, 30, nR))
    oPnlBUT :Align :=CONTROL_ALIGN_TOP

        @ 04, 002 Say "Contrato" PIXEL of oPnlBUT
        @ 02, 025 MsGet oContrato Var cContrato Picture "@!" PIXEL of oPnlBUT SIZE 50,09  VALID If(VldContra(@cContrato, , ), (lSimula:= .F., .T.), .F.) // F3 "CN9"  //VALID DelIHis(nDelIHis)

        @ 04, 102 Say "Planilha" PIXEL of oPnlBUT
        @ 02, 125 MsGet oPlanilha Var cPlanilha Picture "@!" PIXEL of oPnlBUT SIZE 50,09 VALID If(VldContra(@cContrato, @cPlanilha, ), (lSimula:= .F., .T.), .F.) // F3 "CNA"  //VALID DelIHis(nDelIHis)

        @ 04, 202 Say "Item" PIXEL of oPnlBUT
        @ 02, 225 MsGet oItem Var cItem Picture "@!" PIXEL of oPnlBUT SIZE 50,09  VALID  If(VldContra(@cContrato, @cPlanilha, @cItem, oLimRevisa, aDtRevisa), (lSimula:= .F., .T.), .F.) //F3 "CNB"  //VALID DelIHis(nDelIHis)

     	@ 04, 302 Say "Revisão" PIXEL of oPnlBUT   
	    @ 02, 325 MSCOMBOBOX oLimRevisa    VAR cLimRevisa    ITEMS aLimRevisa    SIZE 50,09 PIXEL OF oPnlBUT Valid DtLimRevisa(cLimRevisa, aDtRevisa, @dLimRevisa)
        
        @ 04, 402 Say "Cmp Final" PIXEL of oPnlBUT
        @ 02, 435 MsGet oCmpFim Var cCmpFim Picture "99/9999" PIXEL of oPnlBUT SIZE 50,09  

        @ 02, 502 CHECKBOX oCheck VAR lDetPh5	 PROMPT 'Detalhe PH5'	 SIZE 055,010 OF oPnlBUT PIXEL
        @ 02, 552 BUTTON oBut  PROMPT "Calcular" SIZE 045,010 ACTION ExecHtml(aoEdit, oContrato, oPlanilha, oItem, cLimRevisa, dLimRevisa, cCmpFim )  OF oPnlBUT PIXEL; oBut:nClrText :=0
        @ 02, 602 BUTTON oBut2  PROMPT "Atualizar" SIZE 045,010 ACTION AtuCro(aoEdit, oContrato, oPlanilha, oItem, cLimRevisa, dLimRevisa, cCmpFim )  OF oPnlBUT PIXEL WHEN lSimula; oBut2:nClrText :=0
        
        
    oPnlSup := TPanelCss():New(,,,oDlg)
    oPnlSup :SetCoors(TRect():New( 0,0, nB , nR))
    oPnlSup :Align :=CONTROL_ALIGN_ALLCLIENT

        oFol := TFolder():New(, , aFolder, aFolder, oPnlSup, , , , .T., .F.)
	    //oFol:bSetOption:= {|n| SetFolder(n), .T.}
        oFol:Align := CONTROL_ALIGN_ALLCLIENT
        
        For nx:= 1 to 11

            @ 0,0 SCROLLBOX aoScrSup[nx] HORIZONTAL SIZE 94,206 OF oFol:aDialogs[nx] BORDER
            aoScrSup[nx]:Align := CONTROL_ALIGN_ALLCLIENT

            aoEdit[nx]:= TSimpleEditor():New( 0,0, aoScrSup[nx] , 7000,1000 )
            aoEdit[nx]:Align := CONTROL_ALIGN_LEFT
        Next
            
        
Return

Static Function VldContra(cContrato, cPlanilha, cItem, oLimRevisa, aDtRevisa)
    Local aAreaCNB  := CNB->(GetArea("CNB"))
    Local cChave    := ""
    Local aLimRevisa:= {}
   
	If Left(cContrato, 3)  <> "CON"
		cContrato := Padr("CON" + cContrato, 15)
	EndIf

	cChave := Padr(cContrato, Len(CNB->CNB_CONTRA))

    If cPlanilha <> NIL .and. ! Empty(cPlanilha)
		cPlanilha := StrZero(Val(cPlanilha), 6)
        cChave += Padr(cPlanilha, 6)
    
        If cItem <> NIL .and. ! Empty(cItem)
			cItem := StrZero(Val(cItem), 6)
            cChave += Padr(cItem, 6)
        EndIf
    EndIf

    CNB->(DbSetOrder(3))
    If ! CNB->(DbSeek(xFilial("CNB") + cChave))
        MsgAlert("Contrato não encontrado!")
        RestArea(aAreaCNB)
        Return .F.
    EndIf

    If oLimRevisa <> NIL
        aLimRevisa := {}
        aDtRevisa  := {}
        While CNB->(! Eof() .and. CNB_FILIAL+CNB_CONTRA+CNB_NUMERO+CNB_ITEM == xFilial("CNB") + cContrato + cPlanilha + cItem )
            CN9->(DbSeek(xFilial("CN9") + CNB->CNB_CONTRA + CNB->CNB_REVISA)) 

            aadd(aLimRevisa, CN9->CN9_REVISA )
            aadd(aDtRevisa, {CN9->CN9_REVISA, LastDay(CN9->CN9_DTREV) })
            If CN9->CN9_SITUAC == "05" 
                Exit
            Endif

            CNB->(DbSkip())
        End
        oLimRevisa:SetItems(aLimRevisa)
        oLimRevisa:nAt := len(aLimRevisa)
        oLimRevisa:Refresh()
    EndIf
    RestArea(aAreaCNB)

Return .T.

Static Function DtLimRevisa(cLimRevisa, aDtRevisa, dLimRevisa)
    
    np:= aScan(aDtRevisa, {|x| x[1] == Padr(cLimRevisa, 6) })
    If ! Empty(np)
        If np < len(aDtRevisa)
            dLimRevisa := MONTHSUM(aDtRevisa[np + 1, 2] , -1) 
            If dLimRevisa < aDtRevisa[np, 2]
                dLimRevisa := aDtRevisa[np, 2]
            EndIf
        Else
            dLimRevisa := MonthSum(LastDay(Date()), 1)
        EndIf
    Else
        dLimRevisa := MonthSum(LastDay(Date()), 1)
    EndIf 

Return 

Static Function ExecHtml(aoEdit, oContrato, oPlanilha, oItem, cLimRevisa, dLimRevisa, cCmpFim)
    Local cContrato   := oContrato:cText
    Local cPlanilha   := oPlanilha:cText
    Local cItem       := oItem:cText
    Local lHtml       := .T.
    Local cHtml       := ""
    Local cArqCSV     := ""
    Local cMsgErro    := ""
    Local aAMSave     := {}
    Local lSelSave    := .F.
    Local aDetalhe    := {}
    Local nx          := 0
    Local cHtmAux     := ""

    cLimRevisa := Padr(cLimRevisa, 6)

    FWMsgRun(NIL, {|| U_GCVA094A(cContrato, cPlanilha, cItem, aAMSave, lHtml, @cHtml, cArqCSV, @cMsgErro, cLimRevisa, dLimRevisa, lDetPh5, lSelSave, cCmpFim, , , aDetalhe  )}, "Processando", "Cronograma financeiro" )

    aoEdit[1]:Load(cHtml )
    aoEdit[1]:Refresh()
    If ! Empty(cMsgErro)
        ApMsgAlert(cMsgErro)
    EndIf
    lSimula := .T.

    If Empty(aDetalhe)
        Return 
    EndIf 

    For nx:= 2 to 11
        cHtmAux := RetHtml(nx - 1, aDetalhe) 
        aoEdit[nx]:Load(cHtmAux)
        aoEdit[nx]:Refresh()
    Next

Return 

Static Function RetHtml(nModo, aDetalhe)
    Local aItens  := aDetalhe[nModo]
    Local aCab    := {}
    Local cHtml   := ""
    Local nx
    Local ny

    If nModo == 1 // Situação
        aCab := {"Data Rev", "Situação", "Quantidade", "Valor Unitário", "Condic","CondPg","Unidade","Revisão"}
    ElseIf nModo == 2 // Cancelamento
        aCab := {"Competência", "Operação", "Tipo", "Dt Operação", "Quantidade", "Codigo Motivo", "Multa", "Comp Retroativo", "Situação Anterior"}
    ElseIf nModo == 3 // Carencia
        aCab := {"Operação", "Inicio", "Termino", "Data Oper", "Cliente", "Loja", "Principal"}
    ElseIf nModo == 4 // Bonificação
        aCab := {"Operação", "Inicio", "Termino", "Data Oper", "Cliente", "Loja", "Tipo", "Valor Desconto", "% Desconto"}
    ElseIf nModo == 5 // Reajuste
        aCab := {"Competencia", "Valor Anterior", "Valor Atual", "Revisão", "Indice", "Data reajuste"}
    ElseIf nModo == 6 // Transferenica
        aCab := {"Operação", "Tipo de Movimento", "Periodo" , "Proposta", "Item Proposta", "Quantidade", "Contrato Origem", "Contrato Destino", "Quantidade Origem"}
    ElseIf nModo == 7 // Periodo
        aCab := {"Competência", "Revisão"}
    ElseIf nModo == 8 // Billing
        aCab := {"Competência Fat", "ID", "Valor Total", "Quantidade", "Variante"}
    ElseIf nModo == 9 // Troca
        aCab := {"Competência Cro", "Delta", "ID", "Tipo", "Valor Contrato", "Quantidade Troca", "Situação", "Competência Troca"}
    ElseIf nModo == 10 // Reajuste Extra
        aCab := {"Inicio", "Sequencia", "Linha de Receita", "Porcentagem", "Data Limite", "Compentência Fim", "Corporativo (1-Sim, 2-Não)", "Fase", "Observação"}        
    EndIf

    cHtml += "<br>" + CRLF
    cHtml += "   <table width=100% border=1 cellspacing=0 cellpadding=2 bordercolor='666633'>" + CRLF
    cHtml += "      <tr>" + CRLF
	For nx:= 1 to len(aCab)
		cHtml += "         <td width= 100 align='CENTER'> <font COLOR='GREEN'><b> " + aCab[nx]  + "</font> </td>" + CRLF
	Next
    cHtml += "      </tr>" + CRLF
    
    For nx:= 1 to len(aItens)
        cHtml += "      <tr>" + CRLF
        For ny := 1 to len(aCab)

            uValor := aItens[nx, ny]

            If Valtype(uValor) == "N"
                cHtml += "         <td align='RIGHT'>"
                cHtml += Alltrim(Str(uValor, 15, 2))
            ElseIf Valtype(uValor) == "C"
                cHtml += "         <td align='LEFT'>"
                cHtml += Alltrim(uValor)
            ElseIf Valtype(uValor) == "D"
                cHtml += "         <td align='CENTER'>"
                cHtml += DTOC(uValor)
            ElseIf Valtype(uValor) == "L"
                cHtml += "         <td align='LEFT'>"
                If uValor
                    cHtml += "Sim"
                Else
                    cHtml += "<font COLOR='RED'>"
                    cHtml += "Não"
                EndIf
            EndIf
            cHtml += "</td>" + CRLF
        Next
        cHtml += "      </tr>" + CRLF
    Next 

    cHtml += "   </table>" + CRLF
	cHtml += "<hr>" + CRLF
    cHtml += "<br>" + CRLF
    
Return cHtml


Static Function AtuCro(aoEdit, oContrato, oPlanilha, oItem, cLimRevisa, dLimRevisa, cCmpFim)
    Local cContrato   := oContrato:cText
    Local cPlanilha   := oPlanilha:cText
    Local cItem       := oItem:cText
    Local lHtml       := .F.
    Local cHtml       := ""
    Local cArqCSV     := ""
    Local cMsgErro    := ""
    Local aAMSave     := {}
    Local lSelSave    := .t.
    Local cAMAux      := ""
    Local nx          := 0
    Local aDetalhe    := {}

    cLimRevisa := Padr(cLimRevisa, 6)
    
 
    FWMsgRun(NIL, {|| U_GCVA094A(cContrato, cPlanilha, cItem, aAMSave, lHtml, @cHtml, cArqCSV, @cMsgErro, cLimRevisa, dLimRevisa, lDetPh5, lSelSave, cCmpFim )}, "Processando", "Cronograma financeiro" )
    For nx:= 1 to len(aAMSave) 
        cAMAux := aAMSave[nx]
        U_GCVA103T(cContrato, cLimRevisa, cAMAux)
    Next
    
    aAMSave     := {}
    FWMsgRun(NIL, {|| U_GCVA094A(cContrato, cPlanilha, cItem, aAMSave, .T.  , @cHtml, cArqCSV, @cMsgErro, cLimRevisa, dLimRevisa, lDetPh5, .F., cCmpFim, , , aDetalhe   )}, "Processando", "Cronograma financeiro" )

    aoEdit[1]:Load(cHtml )
    aoEdit[1]:Refresh()
    If ! Empty(cMsgErro)
        ApMsgAlert(cMsgErro)
    EndIf
    lSimula := .T.

    For nx:= 2 to 11
        cHtmAux := RetHtml(nx - 1, aDetalhe) 
        aoEdit[nx]:Load(cHtmAux)
        aoEdit[nx]:Refresh()
    Next 

Return 


/*{Protheus.doc} GCVA094V
Função para calcular o calculo do cronograma financeiro com o uso da classe

@param cContrato
@param cPlanx
@param cItemx
@param aAMSave
@param lGeraHtml
@param cHtml
@param cArqCSV
@param cMsgErro
@param cLimRevisa
@param dLimRevisa
@param lDetPh5
@param lSelSave

<AUTHOR> Sandro Valario
@since 01/10/2018
@return Nil
*/


User Function GCVA094A(cContrato, cPlanx, cItemx, aAMSave, lGeraHtml, cHtml, cArqCSV, cMsgErro, cLimRevisa, dLimRevisa, lDetPh5, lSelSave, cCmpFim, cAMMemCalc, cHtmMCalc, aDetalhe, cSituac)
    Local oCrono      := NIL
    Local cRevisa     := Space(6)
    Local cProduto    := ""
    
    Local cPlanilha   := ""
    Local cItem       := ""
    Local aArea       := GetArea()
    Local aAreaCN9    := {} 
    Local aAreaCNA    := {} 
    Local aAreaCNB    := {} 
    Local aAreaCNC    := {}
    Local aAreaSB1    := {}
    Local aAreaSBM    := {}
    Local aAreaSA1    := {}
    Local aAreaSM0    := {}
    Local aAreaPI2    := {}
    Local aAreaCE1    := {}
    
    Local aCarenVnd   := {}
 
    Local aSituac     := {}
    Local nx          := 0
    Local ny          := 0
    Local np          := 0
    Local cGrpEmp     := ""
    Local cUniNeg     := ""
    Local nRecCNB     := 0
    Local cProdAtu    := ""
    Local oBCmp      
    Local cContac     := ""
    Local cCodIss     := ""
    Local cEstCob     := ""
    Local cCodMun     := ""
    Local cGrpMI  	  := SuperGetMv( 'TI_#GRPMI',, '01|02|04' )
    Local cCmpIni     := "  /    "
    Local nLimCmpProc := SuperGetMv('TI_LMCMPPRO', , 12)
    Local cAnoMesLim  := ""
    Local nRecCN9     := 0 //TIADMVIN-3138

    Default cHtml     := ""
    Default cArqCSV   := ""
    Default cMsgErro  := ""
    Default lDetPh5   := .F.
    Default lSelSave  := .F.
    Default cCmpFim   := "  /    "
    Default cAMMemCalc:= ""
    Default cSituac   := "05" //TIADMVIN-3138

    cAnoMesLim := SubStr(Dtos(monthsum(msdate(),nLimCmpProc)),1,6)   

    If Empty(CmptoAM(cCmpFim)) .and. ! Empty(aAMSave)
        If aAMSave[len(aAMSave)] >= Left(Dtos(Date()), 6) 
            cCmpFim := AMtoCmp(aAMSave[len(aAMSave)])
        EndIf
    EndIf

    If CmptoAM(cCmpFim) > cAnoMesLim
        cAnoMesLim := CmptoAM(cCmpFim)
    EndIf    

    If (Select('SC9') == 0, ChkFile("SC9"), .T.)
    If (Select('CNA') == 0, ChkFile("CNA"), .T.)
    If (Select('CNB') == 0, ChkFile("CNB"), .T.)
    If (Select('CNC') == 0, ChkFile("CNC"), .T.)
    If (Select('SB1') == 0, ChkFile("SB1"), .T.)
    If (Select('SBM') == 0, ChkFile("SBM"), .T.)
    If (Select('SA1') == 0, ChkFile("SA1"), .T.)

    nRecCNB   := CNB->(Recno())
    aAreaCN9  := CN9->(GetArea()) 
    aAreaCNA  := CNA->(GetArea()) 
    aAreaCNB  := CNB->(GetArea()) 
    aAreaCNC  := CNC->(GetArea())
    aAreaSB1  := SB1->(GetArea())
    aAreaSBM  := SBM->(GetArea())
    aAreaSA1  := SA1->(GetArea())

    aAreaSM0  := SM0->(GetArea())
    aAreaPI2  := PI2->(GetArea())
    aAreaCE1  := CE1->(GetArea())

    oCrono := Tgcvxc05():New()
    Begin Sequence

        CN9->(DbSetOrder(7))  //CN9_FILIAL+CN9_NUMERO+CN9_SITUAC
        CN9->(DbSeek(xFilial("CNB") + cContrato + cSituac))

        nRecCN9 := CN9->(Recno())
        CNB->(DbSetOrder(1))
        If CNB->(DbSeek(xFilial("CNB") + cContrato + CN9->CN9_REVISA + cPlanx + cItemx))
            cProdAtu := CNB->CNB_PRODUT
        EndIf

        CNB->(DbSetOrder(3))  // CNB_FILIAL+CNB_CONTRA+CNB_NUMERO+CNB_ITEM + CNB_REVISA
        If ! CNB->(DbSeek(xFilial("CNB") + cContrato + cPlanx + cItemx))
            cMsgErro := "CNB não encontado!"
            Break
        EndIf
        CN9->(DbSetOrder(1)) //CN9_FILIAL + CN9_NUMERO + CN9_REVISA
        While CNB->(! Eof() .and. CNB_FILIAL+CNB_CONTRA+CNB_NUMERO+CNB_ITEM == xFilial("CNB") + cContrato + cPlanx + cItemx )
            If CNB->CNB_PRODUT <> cProdAtu
                CNB->(DbSkip())
                Loop
            EndIf

            CN9->(DbSeek(xFilial("CN9") + CNB->CNB_CONTRA + CNB->CNB_REVISA)) 

            //aadd(aSituac, {Left(Dtos(CNB->CNB_DTSITU),6) , CNB->CNB_SITUAC, CNB->CNB_QUANT, CNB->CNB_VLUNIT, CNB->CNB_CONDIC, CNB->CNB_CONDPG, CNB->CNB_UNINEG, CNB->CNB_REVISA})
            aadd(aSituac, {Left(Dtos(CN9->CN9_DTREV),6) , CNB->CNB_SITUAC, CNB->CNB_QUANT, U_RoundUnid(CNB->CNB_VLUNIT,,CNB->CNB_UNINEG), CNB->CNB_CONDIC, CNB->CNB_CONDPG, CNB->CNB_UNINEG, CNB->CNB_REVISA})

            If CN9->CN9_SITUAC == cSituac .or. (cLimRevisa <> NIL .and. CN9->CN9_REVISA == cLimRevisa)
                cRevisa := CN9->CN9_REVISA
                Exit
            EndIf
 
            CNB->(DbSkip())
        End
        CN9->(DbGoTo(nRecCN9))

        cProduto  := CNB->CNB_PRODUT
        cPlanilha := CNB->CNB_NUMERO
        cItem     := CNB->CNB_ITEM
        cGrpEmp   := CNB->CNB_GRUPO
        cUniNeg   := CNB->CNB_UNINEG
        
        CNA->(DbSetOrder(1))  // CNA_FILIAL+CNA_CONTRA+CNA_REVISA+CNA_NUMERO
        If ! CNA->(DbSeek(xFilial("CNA") + cContrato + cRevisa + cPlanilha))
            cMsgErro := "Planilha não encontada!"
            Break
        EndIf

        CNC->(DbSetOrder(5))  
        If ! CNC->(DbSeek(xFilial("CNC") + cContrato + cRevisa + "01"))
            cMsgErro := "Cliente não encontado CNC!"
            Break
        EndIf

        SB1->(DbSetOrder(1))
        If ! SB1->(DbSeek(xFilial("SB1") + cProduto))
            cMsgErro :="Produto [" + cProduto +  "] não encontado!"
            Break
        EndIf
        cCodIss := SB1->B1_CODISS
        
        SBM->(DbSetOrder(1))
        If ! SBM->(DbSeek(xFilial("SBM") + SB1->B1_GRUPO))
            cMsgErro := "Grupo não encontado!"
            Break
        EndIf

        cContac := SBM->BM_XCONTAC
        cGrupo  := CNB->CNB_GRUPO
        cUniNeg := CNB->CNB_UNINEG

        If ! CNB->CNB_SITUAC $ "APCOTG"
            //cMsgErro := "Contrato com situação diferente de Ativo, Pendente, Cancelado, Transferencia e Troca!"
            Break
        EndIf

        If ! SM0->(DbSeek(cGrupo + cUniNeg))
            cMsgErro := " Nao Existe o Grupo | UniNeg [ "+ AllTrim( cGrupo ) +"' | "+ AllTrim( cUniNeg ) + " ]" + CRLF + "No Cadastro de Empresas do Sistema, Verifique!"
            Break 
        EndIf 

        cEstCob	:= SM0->M0_ESTCOB
		cCodMun	:= SubStr(SM0->M0_CODMUN, 3, 5)

        If ! cEmpAnt $ cGrpMI .and. CNB->CNB_SITUAC $ "AP" .and. (CNB->CNB_TIPREC == "1" .OR. (CNB->CNB_TIPREC <> "1" .AND. Empty(CNB->CNB_PRIMED))) 
            PI2->(dbSetOrder(3))
            If ! PI2->( MsSeek(xFilial( 'PI2' ) + cContac + CNB->CNB_IMPOST))
                cMsgErro :=  'Não existe Conta Contábil cadastrada para o produto '+ AllTrim( cProduto ) + ' do Grupo ' + AllTrim( SB1->B1_GRUPO ) +' Modelo de Tributação:'+ AllTrim( CNB->CNB_IMPOST ) +' e Conta Contábil ' + AllTrim( cContac ) +'.'
                Break
            EndIf 
            CE1->(DbSetOrder(1)) //CE1_FILIAL+CE1_CODISS+CE1_ESTISS+CE1_CMUISS+CE1_PROISS
            If ! CE1->( MsSeek( cUniNeg + cCodIss + cEstCob + cCodMun + cProduto)) 
                If !  CE1->(DbSeek( cUniNeg + cCodIss + cEstCob + cCodMun))
                    cMsgErro :=  'Nao Existe o Codigo de ISS [ '+ AllTrim( cCodIss )  +' ]'+ CRLF +'No Cadastro de Impostos do Sistema, Produto ['+ Alltrim(SB1->B1_COD) +']. Verifique!'
                    Break 
                EndIf 
            EndIf
        EndIf
        PHV->(DbSetOrder(1))  //PHV_FILIAL+PHV_CONTRA+PHV_REVISA+PHV_GRUPO+PHV_UNINEG 
        PHV->(DbSeek(xFilial("PHV") + cContrato + cRevisa + cGrpEmp + cUniNeg ))
        
        oCrono:aSituac     := aClone(aSituac    )

        LoadCXL(oCrono)  // Ajuste de valores 
        LoadP68(oCrono) 
        LoadCli(oCrono)   // Estrutura de rateio
        LoadPH3(oCrono, @cCmpFim)   // Programação, cancelamento e reativação
        LoadPH4(aCarenVnd, oCrono)  // Carencia na venda, Carencia pós venda e Bonificação
        LoadPH8(oCrono)   //Historico das tranferencias
        LoadPHD(oCrono)  // Controle de faturamento de periodico
        LoadPHM(oCrono)  // Carrega bilhetagem
        LoadPHN(oCrono, @cCmpFim)   // Troca de Modalidade
        LoadPQS(oCrono) // Reajuste Extraordinario
        
        LoadPH5(oCrono, @cCmpIni, @cCmpFim, aAMSave) 

        If ! Empty(aAMSave)
            cCmpFim := AMtoCmp(aAMSave[len(aAMSave)])
        EndIf

        If CmpToAM(cCmpFim) > cAnoMesLim
            cCmpFim := AMtoCmp(cAnoMesLim)
        EndIf
        
        LoadCroFin(oCrono, aCarenVnd, dLimRevisa, cCmpIni, cCmpFim)
        
        For nx:= 1 to len(oCrono:aCliente)  
    
            LoadPNG(oCrono, nx) // Carrega os royalties

            SetCliente(oCrono, nx)

            If nx == 1
                oCrono:Cria()
                oCrono:Calc()
            Else
                oCrono:ReCalc()
            EndIf
            
            If lGeraHtml
                cHtml += oCrono:Html(lDetPh5)
            EndIf
            If ! Empty(cArqCSV)
                oCrono:GravaCSV(cArqCSV) 
            EndIf
            If lSelSave
                oCrono:SelCmp(aAMSave)
            EndIf
            If ! Empty(aAMSave)
                oCrono:Salva(aAMSave)
            EndIf
            If ! Empty(cAMMemCalc)  
                oBCmp := oCrono:RetObjCmp(cAMMemCalc)
                If oBCmp <> NIL
                   cHtmMCalc += oBCmp:Formulas()
                EndIf
            EndIf

            For ny := 1 to len(aAMSave)
                np := Ascan(oCrono:aMsgErro, { |x| x[1] == aAMSave[ny] })
                If ! Empty(np)
                    cMsgErro += "Competencia:" + AMtoCmp(oCrono:aMsgErro[np, 1]) + CRLF
                    cMsgErro += oCrono:aMsgErro[np, 2]
                EndIf
            Next 
            
            If ! Empty(cMsgErro) 
                Break 
            EndIf        
        Next  

        If aDetalhe <> Nil
            aDetalhe := {aSituac, aclone(oCrono:aCancela), aclone(oCrono:aCarencia), aclone(oCrono:aBonifica), aclone(oCrono:aReajuste), aclone(oCrono:aTransf), aclone(oCrono:aCtrlPer), aclone(oCrono:aBilling), aclone(oCrono:aTroca), aclone(oCrono:aReajExtra)} 
        EndIf


    End Sequence

    oCrono:FreeChild()
    FreeObj(oCrono)
    oCrono:=NIL 

    RestArea(aAreaSM0)
    RestArea(aAreaPI2)
    RestArea(aAreaCE1)
    RestArea(aAreaSA1)
    RestArea(aAreaSBM)
    RestArea(aAreaSB1)
    RestArea(aAreaCNC)
    RestArea(aAreaCNB)
    RestArea(aAreaCNA)
    RestArea(aAreaCN9)
    RestArea(aArea)

    CNB->(DBGoto(nRecCNB))

Return  


Static Function LoadCroFin(oCrono, aCarenVnd, dLimRevisa, cCmpIni, cCmpFim)

    Local cContrato  := CNB->CNB_CONTRA
    Local cRevisa    := CNB->CNB_REVISA
    Local cPlanilha  := CNB->CNB_NUMERO
    Local cItem      := CNB->CNB_ITEM
	Local nQtdRev    := CNB->CNB_QUANT
	Local cAMAtu     := Left(Dtos(CN9->CN9_DTREV),6)
    Local nQuant     := RetQtdOri()
	Local nVlrUniCtr := U_RoundUnid(CNB->CNB_VLUNIT,,CNB->CNB_UNINEG) 
    Local nVlrTotCtr := nQuant * nVlrUniCtr

    Local cModImpC    := CNB->CNB_IMPOST
    Local cGrpEmp     := CNB->CNB_GRUPO
    Local cUniNeg     := CNB->CNB_UNINEG
    Local cCondPG     := CNB->CNB_CONDPG
    
    Local cCmpFIni    := CNB->CNB_XCOMPE
    Local cSituac     := CNB->CNB_SITUAC
    Local cTipRec     := CNB->CNB_TIPREC
    Local cTipCnt     := CNB->CNB_XTPCNT
    Local dVencto     := CNB->CNB_VENCTO
 
    Local nPeriodico := Val(CNB->CNB_CONDIC) + 1
    Local cProduto   := CNB->CNB_PRODUT
    Local cDescPro   := SB1->B1_DESC
    

    Local cCodISS    := SB1->B1_CODISS
    Local cNotaSe    := CNB->CNB_NOTASE
    Local cMoeda     := CNB->CNB_MOEDA
    Local cMascCC    := CNB->CNB_MASCCC
    Local cStatRM    := CNB->CNB_STATRM
    Local cPropos    := CNB->CNB_PROPOS
    Local cRevPro    := CNB->CNB_PROREV
    Local cFoldPr    := CNB->CNB_XFLDPR
    Local cItmPro    := CNB->CNB_PROITN
    Local cGU        := cGrpEmp + cUniNeg
    Local lBilling   := CNB->CNB_XBILLI == '1' .and. CNB->CNB_XTPCNT  <> '1'
    Local cFreFat    := CNB->CNB_XFREFA
    Local cTpPago    := CNB->CNB_XTPPG
    Local nDiafat    := CNB->CNB_XDIAFA


    Local nQtdMes
    Local dDtIni
    Local dDtFim
    Local dConIni   := ctod("")
    Local dConFin   := ctod("")
    Local cAnoMesI  := ""
    Local cAnoMesF  := ""
    Local nQtxMses   := GetMV("TI_QMSKI"  ,,0)
    Local nFatorJob  := GetMV("TI_FTJOB"  ,,1)

    
    Local nCarenVnd  := aCarenVnd[1]
    Local cCarIniVnd := aCarenVnd[2]
    Local cCarFinVnd := aCarenVnd[3]
    
    Local cLinRec   := SBM->BM_XLINREC
    Local lAudita   := .F.
    Local nImpostC  := 0
    Local cDescMon  := GetDescMon()
    Local lRoyalties:= IsRoyalties()
    Local lCorpora  := Left( AllTrim(SBM->BM_GRUREL), 11 ) == "CORPORATIVO"
        
     // Inicio   
    dDtIni     := CNB->CNB_DTSITU
    dConIni    := dDtIni
    cCmpFIni   := CNB->CNB_XCOMPE

    If nPeriodico == 1  // exclusivo para mensal
        cCmpFIni := AmtoCMP(Left(dtos(dDtIni),6))
        If ! Empty(CmptoAM(cCarIniVnd)) .and. CmptoAM(cCarIniVnd) < CmpToAm(cCmpFIni)  
            cCmpFIni := cCarIniVnd
        EndIf
        If ! Empty(CmptoAM(cCmpIni)) .and. CmptoAM(cCmpIni) < CmpToAm(cCmpFIni)  
            cCmpFIni := cCmpIni
        EndIf
    EndIf    
    
    If Empty(dDtIni) .or. dDtIni < ctod('01/08/16')
        dDtIni := ctod('01/08/16')
    EndIf
     
    cAnoMesI    := Strzero(Year(dDtIni), 4) + StrZero(Month(dDtIni), 2)          // AAAAMM 
       
    dDtFim      := CNB->CNB_VIGFIM   
    
    If dDtIni > dDtFim 
        dDtIni := dDtFim
    EndIf
    dConFin     := dDtFim   
    cAnoMesF    := Strzero(Year(dDtFim), 4) + StrZero(Month(dDtFim), 2)          // AAAAMM 
   
    If dLimRevisa <> NIL .and. Dtos(dDtFim) > Dtos(dLimRevisa)
        dDtFim   := dLimRevisa
    EndIf  
	
	If Empty(dDtFim) .or. Dtos(dDtFim) >=  Dtos(MONTHSUM(Date() ,1))
        If Empty(CmptoAM(cCmpFim))
            dDtFim   := MONTHSUM(Date(), 1)
        Else
            dDtFim   := ctod("01/" + cCmpFim)  
            
            If nQtxMses > 0 //paliativo delay job inicio do mes
	            If AMCalcDif(Left(Dtos(Date()), 6), CmpToAM(cCmpFim)) < nQtxMses
	                dDtFim   := ctod("01/" + cCmpFim)  
	            Else
	                dDtFim   := MONTHSUM(Date(), 12)
	            EndIf
            Endif
        EndIf
    EndIf
    /*
    If dDtFim < Date()
        dDtFim   := Date()
    EndIf
    */
    dDtIni := ctod(dtoc(dDtIni))   
    dDtFim := ctod(dtoc(dDtFim))
    
    nQtdMes     := AMCalcDif(Left(Dtos(dDtIni), 6), Left(Dtos(dDtFim), 6)) + nFatorJob
    
    nImpostC    := U_TFATA012(cProduto, cModImpC, cGrpEmp, cUniNeg, cCondPG)
    U_GVFUNTPM(cCondPG, @nImpostC)
    If ! Empty(cDescMon)
        FWMonitorMsg( cDescMon)
    EndIf
     
    If Left(cLinRec, 2) == "03"   //SMS
        If ! PHV->(Eof()) .and. PHV->PHV_AUDITA == "1"
            lAudita := .T.
        EndIf
    Endif
        
    // Cria o objeto
    oCrono:SetContrato(cContrato, cRevisa, cPlanilha, cItem, cProduto, cDescPro, cTipRec, cTipCnt, dVencto)
    oCrono:SetCarenVnd(nCarenVnd, cCarIniVnd, cCarFinVnd)
    oCrono:SetCNB(cCondPG, cCodISS, cNotaSe, cMoeda, cMascCC, cStatRM, cGrpEmp, cUniNeg, cGU, cFreFat, cTpPago, nDiafat)
    oCrono:SetProposta(cPropos, cRevPro, cItmPro, cFoldPr)
   
    // variveis para calculo do calentario
    oCrono:dDtIni     := dDtIni
    oCrono:dConIni    := dConIni
    oCrono:dConFin    := dConFin
    oCrono:cAnoMesI   := cAnoMesI
    oCrono:cAnoMesF   := cAnoMesF
    oCrono:nQtdMes    := nQtdMes

    // Quantidade e Valores da CNB
	oCrono:cAMAtu     := cAMAtu
	oCrono:nQuant     := nQuant
	oCrono:nQtdRev    := nQtdRev
    oCrono:nVlrUniCtr := nVlrUniCtr
    oCrono:nVlrTotCtr := nVlrTotCtr
    
    oCrono:nPeriodico := nPeriodico

    oCrono:nImpostC   := nImpostC 
    oCrono:cModImpC   := cModImpC
    oCrono:cCmpFIni   := cCmpFIni
    oCrono:cLinRec    := cLinRec  
   
    If lAudita
        oCrono:nPercAudi  := 2.0442 
    EndIf 
    
    oCrono:cSituac    := cSituac

    oCrono:lBilling   := lBilling
    oCrono:lRoyalties := lRoyalties

    oCrono:lCorpora   := lCorpora

    
    
Return


Static Function SetCliente(oCrono, nPosCli)
    Local cCliente   := oCrono:aCliente[nPosCli, 1]
    Local cLoja      := oCrono:aCliente[nPosCli, 2]
    Local cDescCli   := oCrono:aCliente[nPosCli, 3]
    Local lSetorPub := Posicione("AI0", 1, xFilial("AI0") + cCliente + cLoja , "AI0->AI0_SETPUB") == "1"

    oCrono:SetCliente(cCliente, cLoja, cDescCli, lSetorPub)
Return



// *****************************************************************
// funcoes de apoio
// *****************************************************************


Static Function IsRoyalties()
    Local cCdRoyRev := Alltrim(SuperGetMV('TI_BMSTRRY',,"120")) 	
    Local cLcRecRy	:= Left(Alltrim(SuperGetMV('TI_BMCNARY',,"12")), 2)
	Local cAgrpRec	:= Padr(Alltrim(SuperGetMv("TI_AGRLREC")), Len(CNL->CNL_CODAGR)) 	//Agrupador de linha de Receita
    Local aAreaCNL  := CNL->(GetArea("CNL"))
    Local cCdTefRev := Alltrim(GetMV('TI_BMSTTFC',,"130")) 	// TEF - Revenda - Recorrente Variavel
 
    If CNB->CNB_STATRM == cCdTefRev  
        Return .T.
    EndIf

    If ! CNB->CNB_STATRM == cCdRoyRev 
        Return .F.
    EndIf

    If ! CNB->CNB_SITUAC == "A"
        Return .F.
    EndIf

    If ! CNL->(CNL_FILIAL+CNL_CODAGR+CNL_NIVAGR) == xFilial("CNL") + cAgrpRec + cLcRecRy
        CNL->(dbSetOrder(2)) // CNL_FILIAL+CNL_CODAGR+CNL_NIVAGR
	    If ! CNL->(dbSeek(xFilial("CNL") + cAgrpRec + cLcRecRy)) 
            RestArea(aAreaCNL)
            Return .F.
        EndIf
        RestArea(aAreaCNL)
    EndIf
	
	If CNL->CNL_CODIGO == CNA->CNA_TIPPLA // royalties
        Return .T.
    EndIf
 
Return .F.


Static Function GetDescMon()
    Local cDescMon := ""
    Local aInfo    := GetUserInfoArray()
    Local np       := 0

    np := ascan(aInfo, {|x| x[3] == ThreadId()})
 
    If np > 0
        cDescMon  := aInfo[np, 11]
    EndIf 

Return cDescMon

Static Function RetQtdOri()
    Local cContrato := CNB->CNB_CONTRA   
    Local cPlanilha := CNB->CNB_NUMERO
    Local cItem     := CNB->CNB_ITEM
    Local cProduto  := CNB->CNB_PRODUT
    Local nQtdOri   := 0 
    
    Local aAreaCNB  := CNB->(GetArea())  
	Local aAreaCN9  := CN9->(GetArea())  
    Local nRecCNB   := CNB->(Recno())
    Local cChave    := ""

	CN9->(DbSetOrder(1))
	CNB->(DbSetOrder(3))  // CNB_FILIAL+CNB_CONTRA+CNB_NUMERO+CNB_ITEM + CNB_REVISA
	cChave := xFilial("CNB") + cContrato + cPlanilha + cItem
	CNB->(DbSeek(cChave))

	While CNB->(! Eof() .and. CNB_FILIAL+CNB_CONTRA+CNB_NUMERO+CNB_ITEM == xFilial("CNB") + cContrato + cPlanilha + cItem )
		
		If CNB->CNB_PRODUT <> cProduto 
			CNB->(DbSkip())
			Loop
		EndIf
		nQtdOri   := CNB->CNB_QUANT    
		CNB->(DbSkip())
		
		If CNB->(Eof() .or. ! CNB_FILIAL+CNB_CONTRA+CNB_NUMERO+CNB_ITEM == xFilial("CNB") + cContrato + cPlanilha + cItem ) 
			Exit
		EndIf
		
		CN9->(DbSeek(xFilial("CN9") + CNB->CNB_CONTRA + CNB->CNB_REVISA))
		If Empty(CN9->(CN9_DTREV))
			CNB->(DbSkip())
			Loop
		EndIF
		Exit		
	End

    RestArea(aAreaCNB)
	RestArea(aAreaCN9)
    CNB->(DbGoto(nRecCNB))

Return nQtdOri


Static Function LoadCXL(oCrono)
    Local cChave    := ""
    Local aAreaCXL  := CXL->(GetArea("CXL"))
    Local aReajuste := {}
    Local aAjustFin := {}
    Local cAjustFin := GetMv("TI_CXLAJF")

    cChave := xFilial("CXL") + CNB->(CNB_CONTRA + CNB_NUMERO + CNB_ITEM + "C")

    CXL->(DbOrderNickName("CXLCONTRA"))                                                                                                    
    CXL->(DbSeek(cChave))
    While CXL->(! Eof() .and. cChave == CXL_FILIAL + CXL_CONTRA + CXL_PLAN + CXL_ITEMPL + AllTrim(CXL_SITUAC)) 
        
        CXL->(aadd(aReajuste, {Right(CXL_XCOMPE, 4) + Left(CXL_XCOMPE, 2), CXL_VLANT, CXL_VLATU, CXL_REVISA, CXL_INDICE, Dtos(CXL_DTREAJ), CXL_NUMRJ}))

        If Left(CXL->CXL_INDICE, 2) == "AJ" .OR. CXL->CXL_INDICE $ cAjustFin
            CXL->(aadd(aAjustFin, {Right(CXL_XCOMPE, 4) + Left(CXL_XCOMPE, 2), CXL_VLANT, CXL_VLATU, CXL_REVISA, CXL_INDICE, Dtos(CXL_DTREAJ), CXL_NUMRJ, CXL_XCOMIS}))
        EndIf

        CXL->(DbSkip())
    End

    oCrono:aReajuste   := aClone(aReajuste)
    oCrono:aAjustFin   := aClone(aAjustFin)

    
    RestArea(aAreaCXL)

    KillArray(aReajuste)
    KillArray(aAjustFin)
    KillArray(aAreaCXL)
Return

Static Function LoadP68(oCrono)
    Local cChave     := ""
    Local aAreaP68   := P68->(GetArea("P68"))
    Local aP68Intera := {}

    cChave := FwxFilial("P68") + CNB->(CNB_CONTRA + CNB_NUMERO + CNB_ITEM)

    P68->(DbSetOrder(1))   //P68_FILIAL+P68_CONTRA+P68_PLAN+P68_ITEMPL+P68_AMPROC+P68_NUMSEQ                                                                                                 
    P68->(DbSeek(cChave))
    While P68->(! Eof() .and. cChave == P68_FILIAL + P68_CONTRA + P68_PLAN + P68_ITEMPL)       
        P68->(aadd(aP68Intera, {P68_AMPROC, P68_TIPO, P68_NUMSEQ, P68_VLANT, P68_VLATU, P68_REVISA}))
        P68->(DbSkip())
    End

    oCrono:aP68Intera  := aClone(aP68Intera )

    RestArea(aAreaP68)

    KillArray(aAreaP68)
    KillArray(aP68Intera)
Return


Static Function LoadCli(oCrono)
    Local cDescCli  := ""  
    Local aCliente  := {}
    Local aCNCMain  := {}

    cDescCli := Posicione("SA1", 1, xFilial("SA1") + CNC->(CNC_CLIENT + CNC_LOJACL) , "SA1->A1_NREDUZ")
    CNC->(aadd(aCliente, {CNC_CLIENT, CNC_LOJACL, cDescCli}))
    CNC->(AADD(aCNCMain, {CNC_CLIENT, CNC_LOJACL, "", "", 100, "A", "CNC"}))
    oCrono:aCNCMain    := aClone(aCNCMain)

    LoadRateio(aCliente, oCrono)

    oCrono:aCliente    := aClone(aCliente)
    
    KillArray(aCNCMain) 
    KillArray(aCliente) 
Return 

Static Function LoadRateio(aCliente, oCrono)
 
    Local aAreaP70    := P70->(GetArea())
    Local aAreaPHG    := PHG->(GetArea("PHG"))
    Local cP70Excecao := "2"
    Local cP70Histori := "1"
    Local cContrato   := CNB->CNB_CONTRA
    Local cNumero     := CNB->CNB_NUMERO
    Local cItem       := CNB->CNB_ITEM
    Local aExcecao    := {}
    Local aHistoric   := {}
    Local aPHG        := {}
    

    LoadP70(aCliente, cP70Excecao, cContrato, cNumero, cItem, aExcecao)
    LoadP70(aCliente, cP70Histori, cContrato, cNumero, cItem, aHistoric)
    LoadPHG(aCliente, aPHG)

    oCrono:aExcecao    := aClone(aExcecao   )
    oCrono:aHistoric   := aClone(aHistoric  )
    oCrono:aPHG        := aClone(aPHG       )

    P70->(RestArea(aAreaP70))
    PHG->(RestArea(aAreaPHG))

    KillArray(aExcecao) 
    KillArray(aHistoric) 
    KillArray(aPHG) 
    KillArray(aAreaP70) 
    KillArray(aAreaPHG) 
Return

User Function GCVA0947(aCliente, cTpP70, cContrato, cNumero, cItem, aRateio)
    LoadP70(aCliente, cTpP70, cContrato, cNumero, cItem, aRateio)
Return

Static Function LoadP70(aCliente, cTpP70, cContrato, cNumero, cItem, aRateio)
    
    Local cChave   := ""
    Local cDescCli := ""
    Local cFilSA1  := FwxFilial("SA1")
    Local cStatus  := "A"  
    Local nPosCli  := 0 

    Default cTpP70 := "2" 

    P70->(DbSetOrder(3)) //P70_FILIAL+P70_CONTRA+P70_NUMERO+P70_ITEM+P70_TIPO+P70_STATUS+P70_CLIENT+P70_LOJA+P70_AMDE

    cChave := FwxFilial("P70") + cContrato + cNumero + cItem + cTpP70 + cStatus
    P70->(DbSeek(cChave))
    While P70->(! Eof() .and. cChave == P70_FILIAL + P70_CONTRA + P70_NUMERO + P70_ITEM + P70_TIPO + P70_STATUS)

        nPosCli := Ascan(aCliente, {|x| x[1] + x[2]== P70->(P70_CLIENT + P70_LOJA) })

        If nPosCli < 1
            cDescCli := Posicione("SA1", 1, cFilSA1 + P70->(P70_CLIENT + P70_LOJA) , "SA1->A1_NREDUZ")
            P70->(aadd(aCliente, {P70_CLIENT, P70_LOJA, cDescCli}))
            nPosCli := Len(aCliente)
        EndIf

        P70->(AADD(aRateio, {P70_CLIENT, P70_LOJA, P70_AMDE, P70_AMATE, P70_PERRAT, "A", P70_REVHIS}))
        
        P70->(DbSkip())
    End
    
Return


Static Function LoadPHG(aCliente, aPHG)
    Local cChave   := ""
    Local cDescCli := ""
    Local cFilSA1  := FwxFilial("SA1")
    Local nPosCli  := 0 

    PHG->(DbSetOrder(2)) //PHG_FILIAL + PHG_CONTRA + PHG_REVISA + PHG_NUMERO + PHG_ITEM
    cChave := xFilial("PHG") + CNB->(CNB_CONTRA + CNB_REVISA + CNB_NUMERO + CNB_ITEM )
    PHG->(DbSeek(cChave))
    While PHG->(! Eof() .and. cChave == PHG_FILIAL + PHG_CONTRA + PHG_REVISA + PHG_NUMERO + PHG_ITEM)

        nPosCli := Ascan(aCliente, {|x| x[1] + x[2]== PHG->(PHG_CLIENT + PHG_LOJA) })

        If nPosCli < 1
            cDescCli := Posicione("SA1", 1, cFilSA1 + PHG->(PHG_CLIENT + PHG_LOJA) , "SA1->A1_NREDUZ")
            PHG->(aadd(aCliente, {PHG_CLIENT, PHG_LOJA, cDescCli}))
            nPosCli := Len(aCliente)
        EndIf

        PHG->(aadd(aPHG, {PHG_CLIENT, PHG_LOJA, "", "", PHG_PERRAT, "A", "PHG"}))
        PHG->(DbSkip())
    End
    
Return


Static Function LoadPH3(oCrono, cCmpFim)
    Local aAreaPH3 := PH3->(GetArea("PH3"))
    Local cChave   := ""
    Local cOper    := ""  //P-Programado, C-Cancelado, R-Reativado
    Local cTipo    := ""  //T-Total, P-Parcial
    Local dDtOper  := ctod("")
    Local cAMPH3   := ""
	Local cAMCob   := ""
    Local nQtdCNB  := 0
    Local nQtdOpe  := 0
    Local cCodMot  := ""
    Local cSitAnt  := ""
    Local nMulta   := 0
    Local aCancela := {}
    
    PH3->(DbSetOrder(1)) //PH3_FILIAL+PH3_CONTRA+PH3_REVISA+PH3_NUMERO+PH3_PRODUT+PH3_ITEM+PH3_STATUS+PH3_CMPSOL+PH3_CMPCAN+PH3_ITSEQ 
    cChave := xFilial("PH3") + CNB->(CNB_CONTRA + CNB_REVISA + CNB_NUMERO + CNB_PRODUT + CNB_ITEM )
    PH3->(DbSeek(cChave))
    While PH3->(! Eof() .and. cChave == PH3_FILIAL + PH3_CONTRA + PH3_REVISA + PH3_NUMERO + PH3_PRODUT + PH3_ITEM)
        cOper    :=PH3->PH3_STATUS 
        cSitAnt := PH3->PH3_SITUAC
        
        If !(cOper $ 'PCR') 
            PH3->(DbSkip())
            Loop
        EndIf

        If cOper == "P"
            nQtdCNB  := PH3->PH3_QUANT
            nQtdOpe  := PH3->PH3_QTDCAN
            cAMPH3   := CmptoAM(PH3->PH3_CMPCAN)
            If cAMPH3 > Left(Dtos(Date()), 6)
                If cAMPH3 > CmptoAM(cCmpFim)
                    cCmpFim := AMtoCmp(cAMPH3)
                EndIf
            EndIf
            cAMCob := ""
        ElseIf cOper == "C" 
            nQtdCNB  := PH3->PH3_QUANT
            nQtdOpe  := PH3->PH3_QTDCAN
            cAMPH3   := CmptoAM(PH3->PH3_CMPCAN)
            cAMCob   := "" 
            If cAMPH3 > CmptoAM(cCmpFim)
                cCmpFim := AMtoCmp(cAMPH3)
            EndIf
        ElseIf cOper == "R" 
            nQtdCNB := 0
            nQtdOpe := PH3->PH3_QUANT
            cAMCob  := CmptoAM(PH3->PH3_CMPCOB)   
            cAMPH3  := CmptoAM(PH3->PH3_CMPREA)
            If ! Empty(PH3->PH3_CMPCAN)
                PH3->(DbSkip())        
                Loop
            EndIf
            If Empty(cAMCob)
                cAMCob := cAMPH3
            EndIF
            If cAMPH3 > CmptoAM(cCmpFim)
                cCmpFim := AMtoCmp(cAMPH3)
            EndIf
            If empty(cSitAnt)
                cSitAnt := 'A'
            Endif 
        EndIf
        cTipo   :=  If(nQtdCNB == nQtdOpe, "T", "P")
        dDtOper := PH3->PH3_DATA
        cCodMot := PH3->PH3_MOTBC        
        nMulta  := PH3->PH3_VLMULT

        aadd(aCancela, {cAMPH3, cOper, cTipo, dDtOper, nQtdOpe, cCodMot, nMulta, cAMCob, cSitAnt })
        PH3->(DbSkip())
    End
    

    aSort(aCancela,,, {|x,y| x[1] + x[3] < y[1] + y[3] })

    oCrono:aCancela    := aClone(aCancela   )

    RestArea(aAreaPH3)

    KillArray(aAreaPH3)
    KillArray(aCancela)
Return 

Static Function LoadPH4(aCarenVnd, oCrono )
    Local aAreaPH4  := PH4->(GetArea("PH4"))
    Local cChave    := ""
    Local nCaren    := 0
    Local cCarIni   := ""
    Local cCarFin   := ""
    Local dDtOper   := ctod("")
    Local cCliente  := ""
    Local cLoja     := ""
    Local lPrincipal:= .F.
    Local cTpDesc   := ""
    Local nVlrDes   := ""
    Local nPerDes   := ""
    Local cAMOper   := ""
    Local cAMIni    := ""
    Local aAux      := {}
    Local aCarencia := {}
    Local aBonifica := {}
    
    cAMIni := Left(Dtos(CNB->CNB_VIGINI), 6)
    If Empty(cAMIni)
        cAMIni := Left(Dtos(CNB->CNB_DTSITU), 6)
    EndIf

    PH4->(DbSetOrder(1))  //PH4_FILIAL+PH4_CONTRA+PH4_REVISA+PH4_NUMERO+PH4_ITEM+PH4_PRODUT+PH4_TIPO+PH4_STATUS+PH4_CMPINI+PH4_CMPFIM+PH4_ITSEQ                                             
    cChave := xFilial("PH4") + CNB->(CNB_CONTRA + CNB_REVISA + CNB_NUMERO + CNB_ITEM + CNB_PRODUT) + "C"  //B=BonificOper;C=Carencia
    PH4->(DbSeek(cChave))
    // necessario criar laço devido o campo PH4_CMPINI, que está colando fora da ordem
    While PH4->(! Eof() .and. PH4_FILIAL+PH4_CONTRA+PH4_REVISA+PH4_NUMERO+PH4_ITEM+PH4_PRODUT+PH4_TIPO == cChave)
        If PH4->PH4_STATUS == "I"
            PH4->(DbSkip())
            Loop
        EndIf
        cAMOper  := Left(Dtos(PH4->PH4_DTOPER), 6)
        cCarIni  := PH4->PH4_CMPINI
        cCarFin  := PH4->PH4_CMPFIM
        dDtOper  := PH4->PH4_DTOPER
        cCliente := PH4->PH4_CLIENT 
        cLoja    := PH4->PH4_LOJA 

        
        lPrincipal := CNC->(CNC_CLIENT + CNC_LOJACL) == cCliente + cLoja

        aadd(aAux, {cAMOper, cCarIni, cCarFin, dDtOper, cCliente, cLoja, lPrincipal})
        
        PH4->(DbSkip())
    End
    
    If Len(aAux) > 0
        aSort(aAux,,, {|x, y| x[4] < y[4] })
        If aAux[1, 2] == Strzero(Month(CNB->CNB_DTSITU), 2) + "/" + Strzero(Year(CNB->CNB_DTSITU), 4)
            cCarIni := aAux[1, 2]
            cCarFin := aAux[1, 3]
            If cAMIni == CmptoAM(cCarIni) 
                nCaren  := AMCalcDif(cCarIni, cCarFin)
            ElseIf cAMIni > CmptoAM(cCarIni) .and. cAMIni <= CmptoAM(cCarFin)
                nCaren  := AMCalcDif(AMtoCmp(cAMIni), cCarFin) + 1
            EndIf
        EndIf
    EndIf
    aCarenVnd := {nCaren, cCarIni, cCarFin}
    aCarencia := aClone(aAux)

    aSize(aAux, 0)
    aAux := {}
    PH4->(DbSetOrder(1))  //PH4_FILIAL+PH4_CONTRA+PH4_REVISA+PH4_NUMERO+PH4_ITEM+PH4_PRODUT+PH4_TIPO+PH4_STATUS+PH4_CMPINI+PH4_CMPFIM+PH4_ITSEQ                                             
    cChave := xFilial("PH4") + CNB->(CNB_CONTRA + CNB_REVISA + CNB_NUMERO + CNB_ITEM + CNB_PRODUT) + "B" 
    PH4->(DbSeek(cChave))
    // necessario criar laço devido o campo PH4_CMPINI, que está colando fora da ordem
    While PH4->(! Eof() .and. PH4_FILIAL+PH4_CONTRA+PH4_REVISA+PH4_NUMERO+PH4_ITEM+PH4_PRODUT+PH4_TIPO == cChave)
        If PH4->PH4_STATUS == "I"
            PH4->(DbSkip())
            Loop
        EndIf
        cAMOper  := Left(Dtos(PH4->PH4_DTOPER), 6)
        cCarIni  := PH4->PH4_CMPINI
        cCarFin  := PH4->PH4_CMPFIM
        dDtOper  := PH4->PH4_DTOPER
        cCliente := PH4->PH4_CLIENT 
        cLoja    := PH4->PH4_LOJA 
        cTpDesc  := PH4->PH4_TPDESC 
        nVlrDes  := PH4->PH4_VLRDES 
        nPerDes  := PH4->PH4_PERDES 
        aadd(aAux, {cAMOper, cCarIni, cCarFin, dDtOper, cCliente, cLoja, cTpDesc, nVlrDes, nPerDes})
 
        PH4->(DbSkip())
    End
    aBonifica := aClone(aAux)

    oCrono:aCarencia   := aClone(aCarencia  )
    oCrono:aBonifica   := aClone(aBonifica  )

    RestArea(aAreaPH4)

    KillArray(aAreaPH4 )
    KillArray(aAux     )
    KillArray(aCarencia)
    KillArray(aBonifica)
Return  

Static Function LoadPH8(oCrono)
    Local aAreaPH8 := PH8->(GetArea("PH8"))
    Local cChave   := ""
    Local nP       := 0
    
    Local cCompet  := ""
    Local cTM      := ""
    Local cCondic  := ""
    Local cPropos  := ""
    Local cItmPro  := ""
    Local nQtdTrf  := 0
    Local cCtrOri  := ""
    Local cCtrDes  := ""
    Local nQtdOri  := ""
    Local aTransf  := {}
        
    PH8->(DbSetOrder(3))   //PH8_FILIAL+PH8_CONTRA+PH8_NUMERO+PH8_ITEM                                   
    cChave := xFilial("PH8") + CNB->(CNB_CONTRA +  CNB_NUMERO + CNB_ITEM) 
    PH8->(DbSeek(cChave))
    // necessario criar laço devido o campo PH8_CMPINI, que está colando fora da ordem
    While PH8->(! Eof() .and. PH8_FILIAL + PH8_CONTRA +  PH8_NUMERO + PH8_ITEM == cChave)
        cCompet := PH8->PH8_COMPET
        //cCompet := RIGHT(DtoC(PH8->PH8_DTOPER),7)
        cTM     := Left(PH8->PH8_TM, 1)
        cCondic := PH8->PH8_CONDIC
        cPropos := PH8->PH8_PROPOS
        cItmPro := PH8->PH8_ITMPRO
        nQtdTrf := PH8->PH8_QTDTRF
        cCtrOri := PH8->PH8_CTRORI
        cCtrDes := PH8->PH8_CTRDES
        nQtdOri := PH8->PH8_QTDORI
        cAMOper := CmptoAM(cCompet)

        np:= Ascan(aTransf, {|x| x[1] + x[7] + x[8] == cAMOper + cCtrOri + cCtrDes })
        If Empty(np)
            aadd(aTransf, {cAMOper, cTM, cCondic, cPropos, cItmPro, nQtdTrf, cCtrOri, cCtrDes, nQtdOri, 0 })
            np:= Len(aTransf) 
        EndIf

        If cTM == "E"
            aTransf[np, 10 ] += nQtdTrf
        else
            aTransf[np, 10 ] -= nQtdTrf
        EndIf

        PH8->(DbSkip())
    End
    RestArea(aAreaPH8)
    aSort(aTransf,,, {|x,y| x[1] < y[1]} ) 
    oCrono:aTransf     := aClone(aTransf    )

    KillArray(aAreaPH8)
    KillArray(aTransf)

Return 

Static Function LoadPH5(oCrono, cCmpIni, cCmpFim, aAMSave)
    Local aArea     := GetArea()
    Local aAreaPH5  := PH5->(GetArea("PH5"))
    Local cTmpPH5   := GetNextAlias()
    Local cQryPH5   := ""
    Local nx        := 0

    Local cAnoMes   := ""
    Local aLinPH5   := {}

    Local cChavePH6 := ""
    Local cChavePH7 := ""
    Local nPAnoMes  := 0

    Local cAnomesini  := ""
    Local cAnomesFim  := ""
    Local aFinanceiro := {}

    If Len(aAMSave) >  0
        cAnomesini := aAMSave[1]
        cAnomesFim := aAMSave[Len(aAMSave)]
    EndIf
    
    PH6->(DBsetOrder(1))
    PH7->(DBsetOrder(1))

    cQryPH5 := " SELECT PH5.R_E_C_N_O_ RECNO "
    cQryPH5 += " FROM " + RetSqlName("PH5") + " PH5 "
    cQryPH5 += " WHERE PH5.PH5_FILIAL = '" + xFilial("PH5")  + "' "
    cQryPH5 += "   AND PH5.PH5_CONTRA = '" + CNB->CNB_CONTRA + "' "
    cQryPH5 += "   AND PH5.PH5_REVISA = '" + CNB->CNB_REVISA + "' "
    cQryPH5 += "   AND PH5.PH5_NUMERO = '" + CNB->CNB_NUMERO + "' "
    cQryPH5 += "   AND PH5.PH5_ITEM   = '" + CNB->CNB_ITEM   + "' "
    If Len(aAMSave) >  0
        cQryPH5 += " AND PH5.PH5_ANOMES BETWEEN  '" + cAnomesini + "' AND '" + cAnomesFim+ "'"
    EndIf 
    cQryPH5 += " AND PH5.D_E_L_E_T_ = ' ' "

    DbUseArea(.T., "TOPCONN", TcGenQry(NIL, NIL, cQryPH5), cTmpPH5, .T., .F.)

    While ! (cTmpPH5)->(EOF())
        PH5->(DbGoto((cTmpPH5)->RECNO))

        cAnoMes := Right(PH5->PH5_COMPET, 4) + Left(PH5->PH5_COMPET, 2)

        cChavePH6 := xFilial("PH6")+ PH5->(PH5_CONTRA + PH5_REVISA + PH5_NUMERO + PH5_COMPET + PH5_CONDIC + PH5_CLIENT + PH5_LOJA + PH5_CONDPG + PH5_NOTASE + PH5_MOEDA + PH5_MASCCC + PH5_GU + PH5_SEQ)
        PH6->(DbSeek(cChavePH6))

        cChavePH7 := xFilial("PH7") + PH5->(PH5_CONTRA + PH5_REVISA + PH5_NUMERO + PH5_COMPET + PH5_CONDIC + PH5_MOEDA + PH5_GU)
        PH7->(DbSeek(cChavePH7))

        AtuSldCan(aAMSave)

        If DelPH5(aAMSave)
            (cTmpPH5)->(DbSkip())
            Loop
        EndIf

        aSize(aLinPH5, 0)
        aLinPH5 := {}
        For nx:= 1 to PH5->(Fcount()) 
            aadd(aLinPH5, {PH5->(FieldName(nx)), PH5->(FieldGet(nx))} )
        Next
        aadd(aLinPH5, {"PH6_STATUS", PH6->PH6_STATUS} )
        aadd(aLinPH5, {"PH7_STATUS", PH7->PH7_STATUS} )
        aadd(aLinPH5, {"PH5_RECNO" , (cTmpPH5)->RECNO}  )

        aadd(aFinanceiro, aClone(aLinPH5))

        If Empty(CmptoAM(cCmpIni))  .or. cAnoMes < CmptoAM(cCmpIni)
            cCmpIni := AMtoCmp(cAnoMes)
        EndIf

        If cAnoMes > CmptoAM(cCmpFim)
            cCmpFim  := AMtoCmp(cAnoMes)
        EndIf

        (cTmpPH5)->(DbSkip())
    End

    If Select(cTmpPH5) > 0
	    (cTmpPH5)->(DbCloseArea())
    EndIf

    If ! Empty(aFinanceiro)
        nPAnoMes := PH5->(FieldPos("PH5_ANOMES"))
        aSort(aFinanceiro,,, {|x,y| x[nPAnoMes, 2] < y[nPAnoMes, 2] })   
    EndIf

    oCrono:aFinanceiro := aClone(aFinanceiro)

    RestArea(aAreaPH5)
    RestArea(aArea   )

    KillArray(aAreaPH5   )
    KillArray(aArea      )
    KillArray(aFinanceiro)
    KillArray(aLinPH5    )

Return 

Static Function AtuSldCan(aAMSave)
    Local aArea    := GetArea()
    Local aAreaPHP := PHP->(GetArea())
    Local nSldCan  := 0
    Local cAnoMes  := ""
    Local cQuery   := ""
    Local cTMP     := GetNextAlias()
    Local cUniNeg  := PH5->PH5_UNINEG 
    Local cNota    := PH5->PH5_NOTA
    Local cSerie   := PH5->PH5_SERIE
    Local cItemNF  := PH5->PH5_ITEMNF

    cAnoMes := Right(PH5->PH5_COMPET, 4) + Left(PH5->PH5_COMPET, 2)
    If Empty(aScan(aAMSave, cAnoMes))
        Return 
    EndIf
    If Empty(cNota)
        Return 
    EndIf 

    cQuery := "" 
    cQuery += " SELECT PHP.PHP_VLRTOT  "
    cQuery += "   FROM  PHP000 PHP "
    cQuery += "  WHERE PHP.PHP_FILIAL = '" + cUniNeg + "' " 
    cQuery += "    AND PHP.PHP_NFORI  = '" + cNota   + "' " 
    cQuery += "    AND PHP.PHP_SERORI = '" + cSerie  + "' " 
    cQuery += "    AND PHP.PHP_ITORI  = '" + cItemNF + "' " 
    cQuery += "    AND PHP.PHP_TIPO   = '2' "
    cQuery += "    AND PHP.PHP_FLAG   <> 'E' "
    cQuery += "    AND PHP.D_E_L_E_T_ = ' ' "
 
    DbUseArea(.T., "TOPCONN", TcGenQry(NIL, NIL, cQuery), cTMP, .T., .F.)
	If (cTMP)->(Eof())		
        (cTMP)->(DbCloseArea())
        Return 
    EndIf

    While ! (cTMP)->(EOF())
        nSldCan  += (cTMP)->PHP_VLRTOT
        (cTMP)->(DbSkip())
    End 
    (cTMP)->(DbCloseArea())

    PH5->(RecLock("PH5", .F.))
    PH5->PH5_SLDCAN := nSldCan 
    PH5->(MsUnLock())
    RestArea(aAreaPHP)
    RestArea(aArea)

Return 
  

Static Function DelPH5(aAMSave)
    Local cAnoMes    := ""
    Local cStatusPH6 := ""

    cAnoMes := Right(PH5->PH5_COMPET, 4) + Left(PH5->PH5_COMPET, 2)
    If Empty(aScan(aAMSave, cAnoMes))
        Return .f.
    EndIf

    cStatusPH6 := PH6->PH6_STATUS
    If cStatusPH6 $ "01/02/04/05/06/07/08" 
        Return .f.
    EndIf

    If ! Empty(PH5->PH5_NUMMED)
        Return .F.
    EndIf

    PH5->(RecLock("PH5", .F.))
    PH5->(DbDelete())
    PH5->(MsUnLock())

Return .t.


Static Function LoadPNG(oCrono, nPosCli)
    Local cChave     := ""
    Local cCliente   := oCrono:aCliente[nPosCli, 1]
    Local aAreaPNG   := PNG->(GetArea("PNG"))
    Local cCdRoyRev := Alltrim(SuperGetMV('TI_BMSTRRY',,"120")) 	
    Local cCdTefRev := Alltrim(SuperGetMV('TI_BMSTTFC',,"130")) 	// TEF - Revenda - Recorrente Variavel
    Local aRoyalties := {}

    aSize(oCrono:aRoyalties, 0)
    oCrono:aRoyalties := {} 

    If ! IsRoyalties()
        Return
    EndIf

    cChave := xFilial("PNG") + cCliente 

    PNG->(DbSetOrder(1))                                                                                                    
    PNG->(DbSeek(cChave))
    While PNG->(! Eof() .and. cChave == PNG_FILIAL + PNG_CLIENT) 
        
        If PNG->PNG_STATUS == "3" // 3=Log
            PNG->(DbSkip())
            Loop
        EndIf
        
        If PNG->PNG_CINTEG == "000003" .And. CNB->CNB_STATRM <> cCdRoyRev
            PNG->(DbSkip())
            Loop
        EndIf

        If PNG->PNG_CINTEG == "000014" .And. CNB->CNB_STATRM <> cCdTefRev
            PNG->(DbSkip())
            Loop
        EndIf

        aadd(aRoyalties, {CmpToAM(PNG->PNG_COMPET), PNG->PNG_LSTROY, PNG->PNG_DELTA, PNG->PNG_IDPROC, PNG->PNG_STATUS })
        PNG->(DbSkip())
    End

    If Len(aRoyalties) > 0
        aSort(aRoyalties,,, {|x,y| x[1] < y[1] })
    EndIf

    oCrono:aRoyalties := aClone(aRoyalties)

    RestArea(aAreaPNG)

Return

Static Function LoadPHD(oCrono)
    Local cChave   := ""
    Local aAreaPHD := PHD->(GetArea("PHD"))
    Local aCtrlPer := {}
   
    cChave := xFilial("PHD") + CNB->(CNB_CONTRA + CNB_REVISA ) 

    PHD->(DbSetOrder(2))                                                                                                    
    PHD->(DbSeek(cChave))
    While PHD->(!Eof()) 
        If  xFilial("PHD") + CNB->(CNB_CONTRA + CNB_NUMERO + CNB_ITEM + CNB_REVISA )  == PHD->(PHD_FILIAL + PHD_CONTRA + PHD_NUMERO + PHD_ITEM + PHD_REVISA)  
            aadd(aCtrlPer, {CmpToAM(PHD->PHD_COMPET), PHD->PHD_REVISA}) 
        EndIf
        PHD->(DbSkip())
    End
    If Len(aCtrlPer) > 0
        aSort(aCtrlPer,,, {|x,y| x[2] < y[2] })
    EndIf

    oCrono:aCtrlPer    := aClone(aCtrlPer   )

    RestArea(aAreaPHD)

    KillArray(aCtrlPer)
    KillArray(aAreaPHD)

Return

Static Function LoadPHM(oCrono)
    Local cChave := ""
    Local aAreaPHM := PHM->(GetArea("PHM"))
    Local aBilling := {}

    If CNB->CNB_XBILLI <> '1' 
        Return
    EndIf
    
    If CNB->CNB_XTPCNT  == '1'
        Return
    EndIf

    cChave := xFilial("PHM") + CNB->(CNB_CONTRA + CNB_NUMERO + CNB_ITEM )

    PHM->(DbSetOrder(3))                                                                                                    
    PHM->(DbSeek(cChave))
    While PHM->(! Eof() .and. cChave == PHM_FILIAL + PHM_CONTRA + PHM_NUMERO + PHM_ITEM) 
        PHM->(aadd(aBilling, {CmpToAM(PHM_CMPFAT), PHM_IDBILL, PHM_VLTOT, PHM_QUANT, PHM_VARIAV, Recno()})) 
        PHM->(DbSkip())
    End
    If Len(aBilling) > 0
        aSort(aBilling,,, {|x,y| x[1] < y[1] })
    EndIf

    oCrono:aBilling    := aClone(aBilling   )

    RestArea(aAreaPHM)
    
    KillArray(aAreaPHM)
    KillArray(aBilling)

Return

Static Function LoadPHN(oCrono, cCmpFim)
    Local cChave   := ""
    Local aAreaPHN := PHN->(GetArea("PHN"))
    Local cAnoMes  := ""
    Local aTroca   := {}

    cChave := xFilial("PHN") + CNB->(CNB_CONTRA + CNB_REVISA + CNB_NUMERO + CNB_ITEM )
    PHN->(DbSetOrder(5)) // PHN_FILIAL+PHN_CONTRA+PHN_REVISA+PHN_NUMERO+PHN_ITEM+PHN_VIGENT+PHN_AMSCRF                                                                                                     
    PHN->(DbSeek(cChave))
    While PHN->(! Eof() .and. cChave == PHN_FILIAL + PHN_CONTRA + PHN_REVISA +  PHN_NUMERO + PHN_ITEM) 
        If ! PHN->PHN_VIGENT == "A"
            PHN->(DbSkip())
            Loop
        EndIf
        cAnoMes := PHN->PHN_AMSCRF
        PHN->(aadd(aTroca, {PHN_AMSCRF, PHN_DELTPR, PHN_IDPROC, PHN_TIPO, PHN_VLRCTR, PHN_QTTROC, PHN_SITUAC, PHN_AMSTRC, Recno()})) 
        
        If cAnoMes > CmptoAM(cCmpFim)
            cCmpFim  := AMtoCmp(cAnoMes)
        EndIf

        PHN->(DbSkip())
    End
    If Len(aTroca) > 0
        aSort(aTroca,,, {|x,y| x[1] < y[1] })
    EndIf

    oCrono:aTroca := aClone(aTroca)

    RestArea(aAreaPHN)

    KillArray(aAreaPHN)
    KillArray(aTroca)

Return

Static Function LoadPQS(oCrono)
    Local cChave     := ""
    Local aAreaPQS   := PQS->(GetArea())
    Local lCalc      := SuperGetMv("TI_RJTEXT",, .T.)
    Local cAMIni     := "" 
    Local cSeq       := "" 
    Local cLinRec    := "" 
    Local nPerc      := 0 
    Local dDtLim     := ctod("")
    Local cAMFim     := "" 
    Local cCorp      := "" 
    Local cFase      := "" 
    Local cObs       := ""
    Local aReajExtra := {} 

    If ! lCalc
        Return
    EndIf
    PQS->(DbSetOrder(1)) //PQS_FILIAL+PQS_CONTRA+PQS_COMPET+PQS_SEQ
	cChave	:= xFilial("PQS") + CNB->CNB_CONTRA

    PQS->(DbSeek(cChave))
    While ! PQS->(EOF()) .And. cChave == PQS->PQS_FILIAL + PQS->PQS_CONTRA
        cAMIni := CmpToAM(PQS->PQS_COMPET)
        cSeq      := PQS->PQS_SEQ
        cLinRec   := PQS->PQS_LINREC
        nPerc     := PQS->PQS_PERC
        dDtLim    := PQS->PQS_DTLIM
        If PQS->(FieldPos("PQS_CORP")) > 0
            cAMFim    := CmpToAM(PQS->PQS_CMPFIM)
            cCorp     := PQS->PQS_CORP      // 1=Sim,2=Nao
            cFase     := PQS->PQS_FASE      // 1,2,3,4....
        EndIf
        cObs := PQS->PQS_OBS
        
        If PQS->PQS_STATUS <> "C"
            Aadd(aReajExtra, {cAMIni, cSeq, cLinRec, nPerc, dDtLim, cAMFim, cCorp, cFase, cObs, PQS->(Recno()) } )
        EndIf
        PQS->(DbSkip())
    End
    ASort(aReajExtra, , , {|x,y| x[1] + x[2] + StrZero(x[10], 15) < y[1] + y[2] + StrZero(y[10], 15)})

    oCrono:aReajExtra := aClone(aReajExtra)

    RestArea(aAreaPQS)

    KillArray(aAreaPQS  )
    KillArray(aReajExtra)

Return




//Tratamento de datas e competencias
Static Function AMtoCmp(cAM)
    Local cCmp := Right(cAM, 2) + "/" + Left(cAM, 4)

Return cCmp

Static Function CmptoAM(cCmp) 
    Local cAM := Right(cCmp, 4) + Left(cCmp, 2)  

Return cAM


Static Function AMCalcDif(cAMMenor, cAMMaior)
    Local nQtdMes:= 0
    Local cAno   := ""
    Local cMes   := ""
    Local cCmp   := ""

    If "/" $ cAMMenor  // se tiver barra está no formato MM/AAAA
        cAMMenor := Right(cAMMenor,4) + Left(cAMMenor,2)
    EndIf
    If "/" $ cAMMaior
        cAMMaior := Right(cAMMaior,4) + Left(cAMMaior,2)
    EndIf

    If Empty(cAMMenor)
        Return nQtdMes
    EndIf

    cCmp := cAMMenor
    While cCmp < cAMMaior
        cAno := Left(cCmp, 4)
        cMes := Right(cCmp, 2)
        If cMes == "12"
            cMes := "01"
            cAno := Soma1(cAno)
        Else
            cMes := Soma1(cMes)
        EndiF    
        cCmp := cAno + cMes
        nQtdMes++
    End

Return nQtdMes

Static Function KillArray(aArray)
    aSize(aArray, 0)
    aArray := Nil 
Return
